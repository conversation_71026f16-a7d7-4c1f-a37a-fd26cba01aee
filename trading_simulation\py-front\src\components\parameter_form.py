"""
Parameter Form Component
Streamlit component for dynamic parameter forms
"""

import streamlit as st
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, date

def render_parameter_form(
    parameters: Dict[str, Any],
    form_key: str = "parameter_form",
    title: str = "Parameters",
    show_reset: bool = True,
    show_save: bool = True
) -> Dict[str, Any]:
    """
    Render dynamic parameter form
    
    Args:
        parameters: Dictionary of parameter definitions
        form_key: Unique key for the form
        title: Form title
        show_reset: Whether to show reset button
        show_save: Whether to show save button
        
    Returns:
        Dictionary of parameter values
    """
    
    st.markdown(f"### ⚙️ {title}")
    
    values = {}
    
    with st.form(key=form_key):
        # Render parameters based on their type and configuration
        for param_name, param_config in parameters.items():
            value = render_parameter_input(param_name, param_config, form_key)
            values[param_name] = value
        
        # Form buttons
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            if show_save:
                save_clicked = st.form_submit_button("💾 Save", use_container_width=True)
            else:
                save_clicked = False
        
        with col2:
            if show_reset:
                reset_clicked = st.form_submit_button("🔄 Reset", use_container_width=True)
            else:
                reset_clicked = False
        
        # Handle form actions
        if save_clicked:
            st.success("✅ Parameters saved successfully!")
        
        if reset_clicked:
            st.info("🔄 Parameters reset to defaults")
            st.rerun()
    
    return values

def render_parameter_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    form_key: str
) -> Any:
    """
    Render individual parameter input
    
    Args:
        param_name: Parameter name
        param_config: Parameter configuration
        form_key: Form key for unique widget keys
        
    Returns:
        Parameter value
    """
    
    param_type = param_config.get('type', 'text')
    label = param_config.get('label', param_name.replace('_', ' ').title())
    default_value = param_config.get('default')
    help_text = param_config.get('help')
    required = param_config.get('required', False)
    
    # Add required indicator to label
    if required:
        label += " *"
    
    widget_key = f"{form_key}_{param_name}"
    
    # Render input based on parameter type
    if param_type == 'number':
        return render_number_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'slider':
        return render_slider_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'text':
        return render_text_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'select':
        return render_select_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'multiselect':
        return render_multiselect_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'checkbox':
        return render_checkbox_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'date':
        return render_date_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'datetime':
        return render_datetime_input(param_name, param_config, widget_key, label, help_text)
    
    elif param_type == 'color':
        return render_color_input(param_name, param_config, widget_key, label, help_text)
    
    else:
        # Default to text input
        return st.text_input(
            label,
            value=str(default_value) if default_value is not None else "",
            help=help_text,
            key=widget_key
        )

def render_number_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> Union[int, float]:
    """Render number input"""
    
    min_value = param_config.get('min')
    max_value = param_config.get('max')
    default_value = param_config.get('default', 0)
    step = param_config.get('step', 1)
    format_str = param_config.get('format', None)
    
    return st.number_input(
        label,
        min_value=min_value,
        max_value=max_value,
        value=default_value,
        step=step,
        format=format_str,
        help=help_text,
        key=widget_key
    )

def render_slider_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> Union[int, float]:
    """Render slider input"""
    
    min_value = param_config.get('min', 0)
    max_value = param_config.get('max', 100)
    default_value = param_config.get('default', min_value)
    step = param_config.get('step', 1)
    
    return st.slider(
        label,
        min_value=min_value,
        max_value=max_value,
        value=default_value,
        step=step,
        help=help_text,
        key=widget_key
    )

def render_text_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> str:
    """Render text input"""
    
    default_value = param_config.get('default', "")
    max_chars = param_config.get('max_chars')
    placeholder = param_config.get('placeholder')
    multiline = param_config.get('multiline', False)
    
    if multiline:
        return st.text_area(
            label,
            value=default_value,
            max_chars=max_chars,
            placeholder=placeholder,
            help=help_text,
            key=widget_key
        )
    else:
        return st.text_input(
            label,
            value=default_value,
            max_chars=max_chars,
            placeholder=placeholder,
            help=help_text,
            key=widget_key
        )

def render_select_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> Any:
    """Render select input"""
    
    options = param_config.get('options', [])
    default_value = param_config.get('default')
    format_func = param_config.get('format_func')
    
    # Find default index
    index = 0
    if default_value is not None and default_value in options:
        index = options.index(default_value)
    
    return st.selectbox(
        label,
        options=options,
        index=index,
        format_func=format_func,
        help=help_text,
        key=widget_key
    )

def render_multiselect_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> List[Any]:
    """Render multiselect input"""
    
    options = param_config.get('options', [])
    default_values = param_config.get('default', [])
    format_func = param_config.get('format_func')
    
    return st.multiselect(
        label,
        options=options,
        default=default_values,
        format_func=format_func,
        help=help_text,
        key=widget_key
    )

def render_checkbox_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> bool:
    """Render checkbox input"""
    
    default_value = param_config.get('default', False)
    
    return st.checkbox(
        label,
        value=default_value,
        help=help_text,
        key=widget_key
    )

def render_date_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> date:
    """Render date input"""
    
    default_value = param_config.get('default', date.today())
    min_value = param_config.get('min')
    max_value = param_config.get('max')
    
    return st.date_input(
        label,
        value=default_value,
        min_value=min_value,
        max_value=max_value,
        help=help_text,
        key=widget_key
    )

def render_datetime_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> datetime:
    """Render datetime input"""
    
    default_value = param_config.get('default', datetime.now())
    
    # Split into date and time inputs
    col1, col2 = st.columns(2)
    
    with col1:
        date_value = st.date_input(
            f"{label} (Date)",
            value=default_value.date(),
            help=help_text,
            key=f"{widget_key}_date"
        )
    
    with col2:
        time_value = st.time_input(
            f"{label} (Time)",
            value=default_value.time(),
            key=f"{widget_key}_time"
        )
    
    return datetime.combine(date_value, time_value)

def render_color_input(
    param_name: str, 
    param_config: Dict[str, Any], 
    widget_key: str, 
    label: str, 
    help_text: str
) -> str:
    """Render color input"""
    
    default_value = param_config.get('default', '#000000')
    
    return st.color_picker(
        label,
        value=default_value,
        help=help_text,
        key=widget_key
    )

def create_parameter_config(
    param_type: str,
    label: str = None,
    default: Any = None,
    help_text: str = None,
    required: bool = False,
    **kwargs
) -> Dict[str, Any]:
    """
    Create parameter configuration dictionary
    
    Args:
        param_type: Type of parameter
        label: Display label
        default: Default value
        help_text: Help text
        required: Whether parameter is required
        **kwargs: Additional configuration options
        
    Returns:
        Parameter configuration dictionary
    """
    
    config = {
        'type': param_type,
        'label': label,
        'default': default,
        'help': help_text,
        'required': required,
    }
    
    # Add type-specific configuration
    config.update(kwargs)
    
    return config
