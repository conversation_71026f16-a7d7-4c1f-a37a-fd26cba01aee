"""
Application State Management
Handles global application state similar to Redux in React
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json

@dataclass
class ChartData:
    """Chart data structure"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float

@dataclass
class Indicator:
    """Indicator configuration"""
    id: str
    method_id: str
    name: str
    type: str
    enabled: bool
    parameters: Dict[str, Any]
    color: str
    line_style: str = "solid"
    line_width: int = 1
    data: Optional[Dict[str, Any]] = None
    period: Optional[int] = None
    ma_type: Optional[str] = None
    indicator_type: Optional[str] = None

@dataclass
class SelectedInstrument:
    """Selected instrument information"""
    exchange: str
    symbol: str
    timeframe: str
    instrument_id: Optional[int] = None

@dataclass
class ChartSettings:
    """Chart configuration settings"""
    chart_type: str = "candlestick"
    show_volume: bool = True
    show_grid: bool = True
    log_scale: bool = False
    refresh_time: int = 5000
    band_type: str = "none"
    theme: str = "light"

@dataclass
class TradingMethod:
    """Trading method data structure"""
    id: int
    method_id: Optional[int]
    name: str
    method_name: Optional[str]
    description: str
    is_active: bool
    weight: float
    success_rate: Optional[float] = None
    total_profit: Optional[float] = None
    total_trades: Optional[int] = None

@dataclass
class SimulationResult:
    """Simulation result data structure"""
    simulation_id: str
    total_return: float
    win_rate: float
    total_trades: int
    profit_factor: float
    trades: List[Dict[str, Any]] = field(default_factory=list)
    equity_curve: List[Dict[str, Any]] = field(default_factory=list)
    method_performance: List[Dict[str, Any]] = field(default_factory=list)
    drawdown: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class DatabaseTable:
    """Database table information"""
    name: str
    record_count: int
    column_count: int
    size_mb: float
    last_updated: str

@dataclass
class AppState:
    """Global application state"""

    # Data
    chart_data: List[ChartData] = field(default_factory=list)
    exchanges: List[Dict[str, Any]] = field(default_factory=list)
    instruments: List[Dict[str, Any]] = field(default_factory=list)

    # UI State
    selected_instrument: Optional[Dict[str, Any]] = None
    selected_exchange: Optional[str] = None
    selected_symbol: Optional[str] = None
    selected_timeframe: str = "1d"
    chart_settings: ChartSettings = field(default_factory=ChartSettings)
    indicators: List[Indicator] = field(default_factory=list)

    # Trading Methods State
    trading_methods: List[TradingMethod] = field(default_factory=list)
    selected_method: Optional[TradingMethod] = None
    sub_methods: Dict[int, List[Dict[str, Any]]] = field(default_factory=dict)

    # Simulation State
    simulation_results: Optional[SimulationResult] = None
    simulation_history: List[Dict[str, Any]] = field(default_factory=list)
    running_simulation: bool = False
    simulation_progress: float = 0.0

    # Database State
    database_tables: List[DatabaseTable] = field(default_factory=list)
    selected_table: Optional[str] = None
    table_data: Dict[str, Any] = field(default_factory=dict)
    database_backups: List[Dict[str, Any]] = field(default_factory=list)

    # System State
    system_status: Dict[str, Any] = field(default_factory=dict)
    recent_activities: List[Dict[str, Any]] = field(default_factory=list)
    system_metrics: Dict[str, Any] = field(default_factory=dict)

    # Settings State
    application_settings: Dict[str, Any] = field(default_factory=dict)
    user_preferences: Dict[str, Any] = field(default_factory=dict)

    # Loading States
    loading: bool = False
    error: Optional[str] = None
    init_loading: bool = False
    config_loaded: bool = False
    methods_loading: bool = False
    simulation_loading: bool = False
    database_loading: bool = False

    # Chart State (kept for compatibility)
    zoom_state: Optional[Dict[str, Any]] = None
    current_visible_range: Optional[List[int]] = None
    total_data_points: int = 100

    # Theme
    theme: str = "light"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for serialization"""
        return {
            "chart_data": [vars(data) for data in self.chart_data],
            "exchanges": self.exchanges,
            "instruments": self.instruments,
            "selected_instrument": self.selected_instrument,
            "selected_exchange": self.selected_exchange,
            "selected_symbol": self.selected_symbol,
            "selected_timeframe": self.selected_timeframe,
            "chart_settings": vars(self.chart_settings),
            "indicators": [vars(indicator) for indicator in self.indicators],
            "trading_methods": [vars(method) for method in self.trading_methods],
            "selected_method": vars(self.selected_method) if self.selected_method else None,
            "sub_methods": self.sub_methods,
            "simulation_results": vars(self.simulation_results) if self.simulation_results else None,
            "simulation_history": self.simulation_history,
            "running_simulation": self.running_simulation,
            "simulation_progress": self.simulation_progress,
            "database_tables": [vars(table) for table in self.database_tables],
            "selected_table": self.selected_table,
            "table_data": self.table_data,
            "database_backups": self.database_backups,
            "system_status": self.system_status,
            "recent_activities": self.recent_activities,
            "system_metrics": self.system_metrics,
            "application_settings": self.application_settings,
            "user_preferences": self.user_preferences,
            "loading": self.loading,
            "error": self.error,
            "init_loading": self.init_loading,
            "config_loaded": self.config_loaded,
            "methods_loading": self.methods_loading,
            "simulation_loading": self.simulation_loading,
            "database_loading": self.database_loading,
            "zoom_state": self.zoom_state,
            "current_visible_range": self.current_visible_range,
            "total_data_points": self.total_data_points,
            "theme": self.theme,
        }
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load state from dictionary"""
        if "chart_data" in data:
            self.chart_data = [ChartData(**item) for item in data["chart_data"]]
        
        if "exchanges" in data:
            self.exchanges = data["exchanges"]
        
        if "instruments" in data:
            self.instruments = data["instruments"]
        
        if "selected_instrument" in data and data["selected_instrument"]:
            self.selected_instrument = SelectedInstrument(**data["selected_instrument"])
        
        if "chart_settings" in data:
            self.chart_settings = ChartSettings(**data["chart_settings"])
        
        if "indicators" in data:
            self.indicators = [Indicator(**item) for item in data["indicators"]]
        
        if "loading" in data:
            self.loading = data["loading"]
        
        if "error" in data:
            self.error = data["error"]
        
        if "init_loading" in data:
            self.init_loading = data["init_loading"]
        
        if "config_loaded" in data:
            self.config_loaded = data["config_loaded"]
        
        if "zoom_state" in data:
            self.zoom_state = data["zoom_state"]
        
        if "current_visible_range" in data:
            self.current_visible_range = data["current_visible_range"]
        
        if "total_data_points" in data:
            self.total_data_points = data["total_data_points"]
        
        if "theme" in data:
            self.theme = data["theme"]
    
    def save_to_file(self, filename: str) -> None:
        """Save state to file"""
        with open(filename, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    def load_from_file(self, filename: str) -> None:
        """Load state from file"""
        with open(filename, 'r') as f:
            data = json.load(f)
            self.from_dict(data)
    
    def reset(self) -> None:
        """Reset state to defaults"""
        self.chart_data = []
        self.exchanges = []
        self.instruments = []
        self.selected_instrument = None
        self.selected_exchange = None
        self.selected_symbol = None
        self.selected_timeframe = "1d"
        self.chart_settings = ChartSettings()
        self.indicators = []
        self.trading_methods = []
        self.selected_method = None
        self.sub_methods = {}
        self.simulation_results = None
        self.simulation_history = []
        self.running_simulation = False
        self.simulation_progress = 0.0
        self.database_tables = []
        self.selected_table = None
        self.table_data = {}
        self.database_backups = []
        self.system_status = {}
        self.recent_activities = []
        self.system_metrics = {}
        self.application_settings = {}
        self.user_preferences = {}
        self.loading = False
        self.error = None
        self.init_loading = False
        self.config_loaded = False
        self.methods_loading = False
        self.simulation_loading = False
        self.database_loading = False
        self.zoom_state = None
        self.current_visible_range = None
        self.total_data_points = 100
        self.theme = "light"
    
    def add_indicator(self, indicator: Indicator) -> None:
        """Add indicator to state"""
        self.indicators.append(indicator)
    
    def remove_indicator(self, indicator_id: str) -> None:
        """Remove indicator from state"""
        self.indicators = [ind for ind in self.indicators if ind.id != indicator_id]
    
    def update_indicator(self, indicator_id: str, updates: Dict[str, Any]) -> None:
        """Update indicator in state"""
        for indicator in self.indicators:
            if indicator.id == indicator_id:
                for key, value in updates.items():
                    if hasattr(indicator, key):
                        setattr(indicator, key, value)
                break
    
    def toggle_indicator(self, indicator_id: str) -> None:
        """Toggle indicator enabled state"""
        for indicator in self.indicators:
            if indicator.id == indicator_id:
                indicator.enabled = not indicator.enabled
                break
    
    def set_loading(self, loading: bool) -> None:
        """Set loading state"""
        self.loading = loading
    
    def set_error(self, error: Optional[str]) -> None:
        """Set error state"""
        self.error = error
    
    def set_chart_data(self, data: List[ChartData]) -> None:
        """Set chart data"""
        self.chart_data = data
    
    def set_selected_instrument(self, instrument: SelectedInstrument) -> None:
        """Set selected instrument"""
        self.selected_instrument = instrument
    
    def update_chart_settings(self, updates: Dict[str, Any]) -> None:
        """Update chart settings"""
        for key, value in updates.items():
            if hasattr(self.chart_settings, key):
                setattr(self.chart_settings, key, value)

    # Trading Methods State Management
    def set_trading_methods(self, methods: List[Dict[str, Any]]) -> None:
        """Set trading methods"""
        self.trading_methods = [
            TradingMethod(
                id=method.get('id'),
                method_id=method.get('method_id'),
                name=method.get('name', ''),
                method_name=method.get('method_name'),
                description=method.get('description', ''),
                is_active=method.get('is_active', True),
                weight=method.get('weight', 1.0),
                success_rate=method.get('success_rate'),
                total_profit=method.get('total_profit'),
                total_trades=method.get('total_trades')
            )
            for method in methods
        ]

    def update_trading_method(self, method_id: int, updates: Dict[str, Any]) -> None:
        """Update trading method"""
        for method in self.trading_methods:
            if method.id == method_id or method.method_id == method_id:
                for key, value in updates.items():
                    if hasattr(method, key):
                        setattr(method, key, value)
                break

    def set_selected_method(self, method: Optional[TradingMethod]) -> None:
        """Set selected method"""
        self.selected_method = method

    def set_sub_methods(self, method_id: int, sub_methods: List[Dict[str, Any]]) -> None:
        """Set sub-methods for a method"""
        self.sub_methods[method_id] = sub_methods

    # Simulation State Management
    def set_simulation_results(self, results: Dict[str, Any]) -> None:
        """Set simulation results"""
        self.simulation_results = SimulationResult(
            simulation_id=results.get('simulation_id', ''),
            total_return=results.get('total_return', 0.0),
            win_rate=results.get('win_rate', 0.0),
            total_trades=results.get('total_trades', 0),
            profit_factor=results.get('profit_factor', 0.0),
            trades=results.get('trades', []),
            equity_curve=results.get('equity_curve', []),
            method_performance=results.get('method_performance', []),
            drawdown=results.get('drawdown', [])
        )

    def set_simulation_progress(self, progress: float) -> None:
        """Set simulation progress"""
        self.simulation_progress = progress

    def set_running_simulation(self, running: bool) -> None:
        """Set running simulation state"""
        self.running_simulation = running

    def add_simulation_to_history(self, simulation: Dict[str, Any]) -> None:
        """Add simulation to history"""
        self.simulation_history.append(simulation)

    # Database State Management
    def set_database_tables(self, tables: List[Dict[str, Any]]) -> None:
        """Set database tables"""
        self.database_tables = [
            DatabaseTable(
                name=table.get('name', ''),
                record_count=table.get('record_count', 0),
                column_count=table.get('column_count', 0),
                size_mb=table.get('size_mb', 0.0),
                last_updated=table.get('last_updated', '')
            )
            for table in tables
        ]

    def set_selected_table(self, table_name: Optional[str]) -> None:
        """Set selected table"""
        self.selected_table = table_name

    def set_table_data(self, table_name: str, data: Any) -> None:
        """Set table data"""
        self.table_data[table_name] = data

    def set_database_backups(self, backups: List[Dict[str, Any]]) -> None:
        """Set database backups"""
        self.database_backups = backups

    # System State Management
    def set_system_status(self, status: Dict[str, Any]) -> None:
        """Set system status"""
        self.system_status = status

    def set_recent_activities(self, activities: List[Dict[str, Any]]) -> None:
        """Set recent activities"""
        self.recent_activities = activities

    def add_recent_activity(self, activity: Dict[str, Any]) -> None:
        """Add recent activity"""
        self.recent_activities.insert(0, activity)
        # Keep only last 50 activities
        if len(self.recent_activities) > 50:
            self.recent_activities = self.recent_activities[:50]

    def set_system_metrics(self, metrics: Dict[str, Any]) -> None:
        """Set system metrics"""
        self.system_metrics = metrics

    # Settings State Management
    def set_application_settings(self, settings: Dict[str, Any]) -> None:
        """Set application settings"""
        self.application_settings = settings

    def update_application_setting(self, key: str, value: Any) -> None:
        """Update application setting"""
        self.application_settings[key] = value

    def set_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """Set user preferences"""
        self.user_preferences = preferences

    def update_user_preference(self, key: str, value: Any) -> None:
        """Update user preference"""
        self.user_preferences[key] = value

    # Loading State Management
    def set_methods_loading(self, loading: bool) -> None:
        """Set methods loading state"""
        self.methods_loading = loading

    def set_simulation_loading(self, loading: bool) -> None:
        """Set simulation loading state"""
        self.simulation_loading = loading

    def set_database_loading(self, loading: bool) -> None:
        """Set database loading state"""
        self.database_loading = loading