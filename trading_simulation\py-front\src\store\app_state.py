"""
Application State Management
Handles global application state similar to Redux in React
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json

@dataclass
class ChartData:
    """Chart data structure"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float

@dataclass
class Indicator:
    """Indicator configuration"""
    id: str
    method_id: str
    name: str
    type: str
    enabled: bool
    parameters: Dict[str, Any]
    color: str
    line_style: str = "solid"
    line_width: int = 1
    data: Optional[Dict[str, Any]] = None
    period: Optional[int] = None
    ma_type: Optional[str] = None
    indicator_type: Optional[str] = None

@dataclass
class SelectedInstrument:
    """Selected instrument information"""
    exchange: str
    symbol: str
    timeframe: str
    instrument_id: Optional[int] = None

@dataclass
class ChartSettings:
    """Chart configuration settings"""
    chart_type: str = "candlestick"
    show_volume: bool = True
    show_grid: bool = True
    log_scale: bool = False
    refresh_time: int = 5000
    band_type: str = "none"
    theme: str = "light"

@dataclass
class AppState:
    """Global application state"""
    
    # Data
    chart_data: List[ChartData] = field(default_factory=list)
    exchanges: List[Dict[str, Any]] = field(default_factory=list)
    instruments: List[Dict[str, Any]] = field(default_factory=list)
    
    # UI State
    selected_instrument: Optional[SelectedInstrument] = None
    chart_settings: ChartSettings = field(default_factory=ChartSettings)
    indicators: List[Indicator] = field(default_factory=list)
    
    # Loading States
    loading: bool = False
    error: Optional[str] = None
    init_loading: bool = False
    config_loaded: bool = False
    
    # Chart State
    zoom_state: Optional[Dict[str, Any]] = None
    current_visible_range: Optional[List[int]] = None
    total_data_points: int = 100
    
    # Theme
    theme: str = "light"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for serialization"""
        return {
            "chart_data": [vars(data) for data in self.chart_data],
            "exchanges": self.exchanges,
            "instruments": self.instruments,
            "selected_instrument": vars(self.selected_instrument) if self.selected_instrument else None,
            "chart_settings": vars(self.chart_settings),
            "indicators": [vars(indicator) for indicator in self.indicators],
            "loading": self.loading,
            "error": self.error,
            "init_loading": self.init_loading,
            "config_loaded": self.config_loaded,
            "zoom_state": self.zoom_state,
            "current_visible_range": self.current_visible_range,
            "total_data_points": self.total_data_points,
            "theme": self.theme,
        }
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load state from dictionary"""
        if "chart_data" in data:
            self.chart_data = [ChartData(**item) for item in data["chart_data"]]
        
        if "exchanges" in data:
            self.exchanges = data["exchanges"]
        
        if "instruments" in data:
            self.instruments = data["instruments"]
        
        if "selected_instrument" in data and data["selected_instrument"]:
            self.selected_instrument = SelectedInstrument(**data["selected_instrument"])
        
        if "chart_settings" in data:
            self.chart_settings = ChartSettings(**data["chart_settings"])
        
        if "indicators" in data:
            self.indicators = [Indicator(**item) for item in data["indicators"]]
        
        if "loading" in data:
            self.loading = data["loading"]
        
        if "error" in data:
            self.error = data["error"]
        
        if "init_loading" in data:
            self.init_loading = data["init_loading"]
        
        if "config_loaded" in data:
            self.config_loaded = data["config_loaded"]
        
        if "zoom_state" in data:
            self.zoom_state = data["zoom_state"]
        
        if "current_visible_range" in data:
            self.current_visible_range = data["current_visible_range"]
        
        if "total_data_points" in data:
            self.total_data_points = data["total_data_points"]
        
        if "theme" in data:
            self.theme = data["theme"]
    
    def save_to_file(self, filename: str) -> None:
        """Save state to file"""
        with open(filename, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    def load_from_file(self, filename: str) -> None:
        """Load state from file"""
        with open(filename, 'r') as f:
            data = json.load(f)
            self.from_dict(data)
    
    def reset(self) -> None:
        """Reset state to defaults"""
        self.chart_data = []
        self.exchanges = []
        self.instruments = []
        self.selected_instrument = None
        self.chart_settings = ChartSettings()
        self.indicators = []
        self.loading = False
        self.error = None
        self.init_loading = False
        self.config_loaded = False
        self.zoom_state = None
        self.current_visible_range = None
        self.total_data_points = 100
        self.theme = "light"
    
    def add_indicator(self, indicator: Indicator) -> None:
        """Add indicator to state"""
        self.indicators.append(indicator)
    
    def remove_indicator(self, indicator_id: str) -> None:
        """Remove indicator from state"""
        self.indicators = [ind for ind in self.indicators if ind.id != indicator_id]
    
    def update_indicator(self, indicator_id: str, updates: Dict[str, Any]) -> None:
        """Update indicator in state"""
        for indicator in self.indicators:
            if indicator.id == indicator_id:
                for key, value in updates.items():
                    if hasattr(indicator, key):
                        setattr(indicator, key, value)
                break
    
    def toggle_indicator(self, indicator_id: str) -> None:
        """Toggle indicator enabled state"""
        for indicator in self.indicators:
            if indicator.id == indicator_id:
                indicator.enabled = not indicator.enabled
                break
    
    def set_loading(self, loading: bool) -> None:
        """Set loading state"""
        self.loading = loading
    
    def set_error(self, error: Optional[str]) -> None:
        """Set error state"""
        self.error = error
    
    def set_chart_data(self, data: List[ChartData]) -> None:
        """Set chart data"""
        self.chart_data = data
    
    def set_selected_instrument(self, instrument: SelectedInstrument) -> None:
        """Set selected instrument"""
        self.selected_instrument = instrument
    
    def update_chart_settings(self, updates: Dict[str, Any]) -> None:
        """Update chart settings"""
        for key, value in updates.items():
            if hasattr(self.chart_settings, key):
                setattr(self.chart_settings, key, value) 