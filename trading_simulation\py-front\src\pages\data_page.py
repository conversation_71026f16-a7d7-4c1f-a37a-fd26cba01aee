"""
Data Management Page
Handles market data, exchanges, and data import/export operations
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
from services.api_service import APIService
from store.app_state import AppState
import io

def data_page():
    """Main data management page"""
    
    st.title("📊 Data Management")
    st.markdown("Manage market data, exchanges, and data import/export operations.")
    
    # Get application state
    app_state: AppState = st.session_state.app_state
    api_service: APIService = st.session_state.api_service
    
    # Create tabs for different sections
    tab1, tab2, tab3, tab4 = st.tabs([
        "🏢 Exchanges", 
        "📈 Market Data", 
        "📥 Import/Export", 
        "🔍 Data Quality"
    ])
    
    with tab1:
        exchanges_tab(api_service, app_state)
    
    with tab2:
        market_data_tab(api_service, app_state)
    
    with tab3:
        import_export_tab(api_service, app_state)
    
    with tab4:
        data_quality_tab(api_service, app_state)

def exchanges_tab(api_service: APIService, app_state: AppState):
    """Exchange management tab"""
    
    st.subheader("Exchange Management")
    
    # Load exchanges
    if 'exchanges_data' not in st.session_state:
        load_exchanges_data(api_service)
    
    exchanges = st.session_state.get('exchanges_data', [])
    
    # Add new exchange
    with st.expander("➕ Add New Exchange"):
        col1, col2 = st.columns(2)
        
        with col1:
            exchange_name = st.text_input("Exchange Name")
            exchange_url = st.text_input("API URL")
        
        with col2:
            exchange_type = st.selectbox("Exchange Type", ["Crypto", "Stock", "Forex", "Commodity"])
            is_active = st.checkbox("Active", value=True)
        
        if st.button("Add Exchange"):
            add_exchange(exchange_name, exchange_url, exchange_type, is_active, api_service)
    
    # Display existing exchanges
    st.markdown("### Existing Exchanges")
    
    if exchanges:
        for exchange in exchanges:
            display_exchange_card(exchange, api_service)
    else:
        st.info("No exchanges configured. Add an exchange to get started.")

def display_exchange_card(exchange: Dict[str, Any], api_service: APIService):
    """Display a single exchange card"""
    
    with st.container():
        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
        
        with col1:
            st.markdown(f"**{exchange.get('name', 'Unknown Exchange')}**")
            st.caption(f"Type: {exchange.get('type', 'Unknown')} | URL: {exchange.get('url', 'N/A')}")
        
        with col2:
            status = exchange.get('is_active', False)
            status_color = "🟢" if status else "🔴"
            st.markdown(f"{status_color} {'Active' if status else 'Inactive'}")
        
        with col3:
            instruments_count = exchange.get('instruments_count', 0)
            st.metric("Instruments", instruments_count)
        
        with col4:
            if st.button("⚙️ Configure", key=f"config_exchange_{exchange.get('id')}"):
                configure_exchange(exchange, api_service)
        
        st.divider()

def market_data_tab(api_service: APIService, app_state: AppState):
    """Market data management tab"""
    
    st.subheader("Market Data Overview")
    
    # Data summary
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Records", "1,250,000")
    
    with col2:
        st.metric("Exchanges", "5")
    
    with col3:
        st.metric("Instruments", "125")
    
    with col4:
        st.metric("Last Update", "2 min ago")
    
    # Data filtering
    st.markdown("### Data Explorer")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        selected_exchange = st.selectbox(
            "Select Exchange",
            ["All", "Binance", "Coinbase", "Kraken"],
            key="data_exchange_filter"
        )
    
    with col2:
        selected_symbol = st.selectbox(
            "Select Symbol",
            ["All", "BTCUSDT", "ETHUSDT", "ADAUSDT"],
            key="data_symbol_filter"
        )
    
    with col3:
        selected_timeframe = st.selectbox(
            "Select Timeframe",
            ["All", "1m", "5m", "15m", "1h", "4h", "1d"],
            key="data_timeframe_filter"
        )
    
    # Data preview
    if st.button("🔍 Load Data Preview"):
        load_data_preview(selected_exchange, selected_symbol, selected_timeframe, api_service)
    
    # Display data preview
    if 'data_preview' in st.session_state:
        st.markdown("### Data Preview")
        df = st.session_state.data_preview
        st.dataframe(df, use_container_width=True)
        
        # Data statistics
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Data Statistics:**")
            st.write(df.describe())
        
        with col2:
            st.markdown("**Data Info:**")
            buffer = io.StringIO()
            df.info(buf=buffer)
            st.text(buffer.getvalue())

def import_export_tab(api_service: APIService, app_state: AppState):
    """Data import/export tab"""
    
    st.subheader("Data Import/Export")
    
    # Import section
    st.markdown("### 📥 Import Data")
    
    import_method = st.radio(
        "Import Method",
        ["CSV File Upload", "API Connection", "Database Import"],
        horizontal=True
    )
    
    if import_method == "CSV File Upload":
        uploaded_file = st.file_uploader(
            "Choose CSV file",
            type=['csv'],
            help="Upload market data in CSV format"
        )
        
        if uploaded_file is not None:
            # Preview uploaded data
            df = pd.read_csv(uploaded_file)
            st.markdown("**File Preview:**")
            st.dataframe(df.head(), use_container_width=True)
            
            # Import configuration
            col1, col2 = st.columns(2)
            
            with col1:
                exchange_for_import = st.selectbox("Target Exchange", ["Binance", "Coinbase", "Kraken"])
                symbol_for_import = st.text_input("Symbol", value="BTCUSDT")
            
            with col2:
                timeframe_for_import = st.selectbox("Timeframe", ["1m", "5m", "15m", "1h", "4h", "1d"])
                overwrite_existing = st.checkbox("Overwrite Existing Data")
            
            if st.button("📥 Import Data"):
                import_csv_data(df, exchange_for_import, symbol_for_import, timeframe_for_import, overwrite_existing, api_service)
    
    elif import_method == "API Connection":
        st.info("API connection import functionality will be implemented here.")
    
    elif import_method == "Database Import":
        st.info("Database import functionality will be implemented here.")
    
    # Export section
    st.markdown("---")
    st.markdown("### 📤 Export Data")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        export_exchange = st.selectbox("Exchange", ["All", "Binance", "Coinbase", "Kraken"], key="export_exchange")
    
    with col2:
        export_symbol = st.selectbox("Symbol", ["All", "BTCUSDT", "ETHUSDT", "ADAUSDT"], key="export_symbol")
    
    with col3:
        export_format = st.selectbox("Format", ["CSV", "JSON", "Excel"])
    
    if st.button("📤 Export Data"):
        export_data(export_exchange, export_symbol, export_format, api_service)

def data_quality_tab(api_service: APIService, app_state: AppState):
    """Data quality and validation tab"""
    
    st.subheader("Data Quality Analysis")
    
    # Quality metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Data Completeness", "98.5%", delta="0.2%")
    
    with col2:
        st.metric("Missing Values", "1,250", delta="-150")
    
    with col3:
        st.metric("Duplicate Records", "45", delta="-12")
    
    with col4:
        st.metric("Outliers Detected", "23", delta="-5")
    
    # Quality checks
    st.markdown("### Quality Checks")
    
    if st.button("🔍 Run Quality Analysis"):
        run_quality_analysis(api_service)
    
    # Display quality issues
    if 'quality_issues' in st.session_state:
        issues = st.session_state.quality_issues
        
        if issues:
            st.markdown("### Issues Found")
            
            for issue in issues:
                severity = issue.get('severity', 'info')
                icon = {'error': '❌', 'warning': '⚠️', 'info': 'ℹ️'}.get(severity, 'ℹ️')
                
                st.markdown(f"{icon} **{issue.get('type', 'Unknown')}:** {issue.get('description', 'No description')}")
        else:
            st.success("✅ No data quality issues found!")
    
    # Data cleaning options
    st.markdown("### Data Cleaning")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🧹 Remove Duplicates"):
            remove_duplicates(api_service)
    
    with col2:
        if st.button("🔧 Fix Missing Values"):
            fix_missing_values(api_service)

# Helper Functions

def load_exchanges_data(api_service: APIService):
    """Load exchanges data"""
    
    try:
        # Placeholder data - would be actual API call
        exchanges = [
            {
                'id': 1,
                'name': 'Binance',
                'type': 'Crypto',
                'url': 'https://api.binance.com',
                'is_active': True,
                'instruments_count': 500
            },
            {
                'id': 2,
                'name': 'Coinbase',
                'type': 'Crypto',
                'url': 'https://api.coinbase.com',
                'is_active': True,
                'instruments_count': 200
            },
            {
                'id': 3,
                'name': 'Kraken',
                'type': 'Crypto',
                'url': 'https://api.kraken.com',
                'is_active': False,
                'instruments_count': 150
            }
        ]
        
        st.session_state.exchanges_data = exchanges
    
    except Exception as e:
        st.error(f"Failed to load exchanges: {str(e)}")

def add_exchange(name: str, url: str, exchange_type: str, is_active: bool, api_service: APIService):
    """Add a new exchange"""
    
    if not name or not url:
        st.error("Please provide both exchange name and URL")
        return
    
    try:
        # This would be an actual API call
        st.success(f"✅ Exchange '{name}' added successfully!")
        
        # Reload exchanges data
        load_exchanges_data(api_service)
        st.rerun()
    
    except Exception as e:
        st.error(f"Failed to add exchange: {str(e)}")

def configure_exchange(exchange: Dict, api_service: APIService):
    """Configure an exchange"""
    st.info(f"Configuration for {exchange.get('name')} will be implemented here.")

def load_data_preview(exchange: str, symbol: str, timeframe: str, api_service: APIService):
    """Load data preview"""
    
    try:
        # Generate sample data
        import numpy as np
        from datetime import datetime, timedelta
        
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='1H')
        
        data = {
            'timestamp': dates,
            'open': np.random.uniform(40000, 45000, len(dates)),
            'high': np.random.uniform(45000, 50000, len(dates)),
            'low': np.random.uniform(35000, 40000, len(dates)),
            'close': np.random.uniform(40000, 45000, len(dates)),
            'volume': np.random.uniform(100, 1000, len(dates))
        }
        
        df = pd.DataFrame(data)
        st.session_state.data_preview = df
        
        st.success("✅ Data preview loaded successfully!")
    
    except Exception as e:
        st.error(f"Failed to load data preview: {str(e)}")

def import_csv_data(df: pd.DataFrame, exchange: str, symbol: str, timeframe: str, overwrite: bool, api_service: APIService):
    """Import CSV data"""
    
    try:
        # This would be an actual API call
        st.success(f"✅ Imported {len(df)} records for {exchange}:{symbol} ({timeframe})")
    
    except Exception as e:
        st.error(f"Failed to import data: {str(e)}")

def export_data(exchange: str, symbol: str, format: str, api_service: APIService):
    """Export data"""
    
    try:
        # This would be an actual API call
        st.success(f"✅ Data exported successfully in {format} format")
    
    except Exception as e:
        st.error(f"Failed to export data: {str(e)}")

def run_quality_analysis(api_service: APIService):
    """Run data quality analysis"""
    
    try:
        # Mock quality issues
        issues = [
            {
                'type': 'Missing Values',
                'description': 'Found 150 missing volume values in BTCUSDT data',
                'severity': 'warning'
            },
            {
                'type': 'Outliers',
                'description': 'Detected 5 price outliers in ETHUSDT data',
                'severity': 'info'
            }
        ]
        
        st.session_state.quality_issues = issues
        st.success("✅ Quality analysis completed!")
    
    except Exception as e:
        st.error(f"Quality analysis failed: {str(e)}")

def remove_duplicates(api_service: APIService):
    """Remove duplicate records"""
    st.success("✅ Removed 45 duplicate records")

def fix_missing_values(api_service: APIService):
    """Fix missing values"""
    st.success("✅ Fixed 150 missing values using interpolation")
