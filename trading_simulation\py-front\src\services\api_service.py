"""
API Service Module
Handles all communication with the backend API
"""

import requests
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from config.app_config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIService:
    """API service for communicating with the backend"""
    
    def __init__(self):
        self.base_url = config.api_base_url
        self.timeout = config.api_timeout
        self.endpoints = config.get_api_endpoints()
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request to API"""
        try:
            url = f"{self.base_url}{endpoint}"
            logger.info(f"Making {method} request to {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            response.raise_for_status()
            
            if response.headers.get('content-type', '').startswith('application/json'):
                return response.json()
            else:
                return {"success": True, "data": response.content}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"success": False, "error": str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return {"success": False, "error": "Invalid JSON response"}
    
    def get_init_config(self) -> Dict[str, Any]:
        """Get initialization configuration"""
        return self._make_request('GET', '/api/v1/init-config')
    
    def save_init_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save initialization configuration"""
        return self._make_request('POST', '/api/v1/init-config', json=config_data)
    
    def change_instrument(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Change selected instrument"""
        params = {
            'exchange': exchange,
            'symbol': symbol,
            'timeframe': timeframe
        }
        return self._make_request('POST', '/api/v1/instrument/change', params=params)
    
    def get_chart_data(self, instrument_id: int, limit: int = 1000) -> Dict[str, Any]:
        """Get chart data for instrument"""
        return self._make_request('GET', f'/api/v1/chart/data/{instrument_id}', params={'limit': limit})
    
    def get_chart_config(self, instrument_id: int) -> Dict[str, Any]:
        """Get chart configuration for instrument"""
        return self._make_request('GET', f'/api/v1/chart/config/{instrument_id}')
    
    def update_chart_config(self, instrument_id: int, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update chart configuration for instrument"""
        return self._make_request('POST', f'/api/v1/chart/config/{instrument_id}', json=config_data)
    
    def get_chart_config_by_path(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Get chart configuration by path"""
        return self._make_request('GET', f'/api/v1/chart/config/{exchange}/{symbol}/{timeframe}')
    
    def calculate_indicator(self, instrument_id: int, indicator_name: str, 
                          parameters: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Calculate technical indicator"""
        data = {
            'instrument_id': instrument_id,
            'indicator_name': indicator_name,
            'parameters': parameters
        }
        
        if options:
            data['options'] = options
            
        return self._make_request('POST', '/api/v1/indicators/calculate', json=data)
    
    def get_exchanges(self) -> Dict[str, Any]:
        """Get list of exchanges"""
        return self._make_request('GET', '/api/v1/database/exchanges')
    
    def get_instruments(self, exchange_id: Optional[int] = None) -> Dict[str, Any]:
        """Get list of instruments"""
        if exchange_id:
            return self._make_request('GET', f'/api/v1/data/instruments/{exchange_id}')
        else:
            return self._make_request('GET', '/api/instruments')
    
    def add_instrument(self, instrument_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new instrument"""
        return self._make_request('POST', '/api/v1/data/instruments', json=instrument_data)
    
    def run_simulation(self, simulation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Run trading simulation"""
        return self._make_request('POST', '/api/v1/simulation/run', json=simulation_config)
    
    def get_simulation_results(self, simulation_id: str) -> Dict[str, Any]:
        """Get simulation results"""
        return self._make_request('GET', f'/api/v1/simulation/results/{simulation_id}')
    
    def get_simulation_history(self) -> Dict[str, Any]:
        """Get simulation history"""
        return self._make_request('GET', '/api/v1/simulation/history')
    
    def get_database_status(self) -> Dict[str, Any]:
        """Get database status"""
        return self._make_request('GET', '/api/v1/database/status')
    
    def backup_database(self) -> Dict[str, Any]:
        """Backup database"""
        return self._make_request('POST', '/api/v1/database/backup')
    
    def import_csv_data(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """Import CSV data"""
        files = {'file': (filename, file_data, 'text/csv')}
        return self._make_request('POST', '/api/v1/data/import/csv', files=files)
    
    def validate_import(self, validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate import data"""
        return self._make_request('POST', '/api/v1/data/import/validate', json=validation_data)
    
    def export_data(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Export data"""
        return self._make_request('GET', f'/api/v1/data/export/{exchange}/{symbol}/{timeframe}')
    
    def get_methods_list(self) -> Dict[str, Any]:
        """Get list of trading methods"""
        return self._make_request('GET', '/api/v1/methods/list')
    
    def get_method_details(self, method_id: int) -> Dict[str, Any]:
        """Get method details"""
        return self._make_request('GET', f'/api/v1/methods/{method_id}')
    
    def get_method_submethods(self, method_id: int) -> Dict[str, Any]:
        """Get method submethods"""
        return self._make_request('GET', f'/api/v1/methods/{method_id}/submethods')
    
    def update_method_config(self, method_id: int, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update method configuration"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/config', json=config_data)
    
    def test_method(self, method_id: int) -> Dict[str, Any]:
        """Test method"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/test')
    
    def optimize_method(self, method_id: int, optimization_config: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize method"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/optimize', json=optimization_config)
    
    def health_check(self) -> Dict[str, Any]:
        """Health check endpoint"""
        return self._make_request('GET', '/health')

    # Trading Methods API
    def get_trading_methods(self, instrument_id: int) -> Dict[str, Any]:
        """Get trading methods for instrument"""
        return self._make_request('GET', f'/api/v1/trading-methods?instrument_id={instrument_id}')

    def update_method_weight(self, method_id: int, weight: float) -> Dict[str, Any]:
        """Update method weight"""
        return self._make_request('POST', f'/api/v1/trading-methods/{method_id}/weight', json={'weight': weight})

    def toggle_method_active(self, method_id: int, is_active: bool) -> Dict[str, Any]:
        """Toggle method active status"""
        return self._make_request('POST', f'/api/v1/trading-methods/{method_id}/active', json={'is_active': is_active})

    def get_sub_methods(self, method_id: int) -> Dict[str, Any]:
        """Get sub-methods for a method"""
        return self._make_request('GET', f'/api/v1/trading-methods/{method_id}/sub-methods')

    def update_sub_method_config(self, sub_method_id: int, config: Dict[str, Any]) -> Dict[str, Any]:
        """Update sub-method configuration"""
        return self._make_request('POST', f'/api/v1/trading-methods/sub-methods/{sub_method_id}/config', json=config)

    # Simulation API
    def get_simulation_config(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Get simulation configuration"""
        return self._make_request('GET', f'/api/v1/simulation/config/{exchange}/{symbol}/{timeframe}')

    def update_simulation_config(self, exchange: str, symbol: str, timeframe: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Update simulation configuration"""
        return self._make_request('POST', f'/api/v1/simulation/config/{exchange}/{symbol}/{timeframe}', json=config)

    def get_simulation_forecast(self, method_id: int) -> Dict[str, Any]:
        """Get simulation forecast"""
        return self._make_request('GET', f'/api/v1/simulation/forecast/{method_id}')

    def stop_simulation(self, simulation_id: str) -> Dict[str, Any]:
        """Stop running simulation"""
        return self._make_request('POST', f'/api/v1/simulation/{simulation_id}/stop')

    # Data Management API
    def add_exchange(self, exchange_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new exchange"""
        return self._make_request('POST', '/api/v1/data/exchanges', json=exchange_data)

    def update_exchange(self, exchange_id: int, exchange_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update exchange"""
        return self._make_request('PUT', f'/api/v1/data/exchanges/{exchange_id}', json=exchange_data)

    def delete_exchange(self, exchange_id: int) -> Dict[str, Any]:
        """Delete exchange"""
        return self._make_request('DELETE', f'/api/v1/data/exchanges/{exchange_id}')

    def get_market_data(self, exchange: str, symbol: str, timeframe: str, limit: int = 1000) -> Dict[str, Any]:
        """Get market data"""
        params = {'limit': limit}
        return self._make_request('GET', f'/api/v1/data/market/{exchange}/{symbol}/{timeframe}', params=params)

    def import_market_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Import market data"""
        return self._make_request('POST', '/api/v1/data/import/market', json=data)

    def export_market_data(self, exchange: str, symbol: str, timeframe: str, format: str = 'csv') -> Dict[str, Any]:
        """Export market data"""
        params = {'format': format}
        return self._make_request('GET', f'/api/v1/data/export/{exchange}/{symbol}/{timeframe}', params=params)

    def validate_data_quality(self, exchange: str = None, symbol: str = None) -> Dict[str, Any]:
        """Validate data quality"""
        params = {}
        if exchange:
            params['exchange'] = exchange
        if symbol:
            params['symbol'] = symbol
        return self._make_request('GET', '/api/v1/data/quality/validate', params=params)

    def fix_data_quality_issues(self, issue_types: List[str]) -> Dict[str, Any]:
        """Fix data quality issues"""
        return self._make_request('POST', '/api/v1/data/quality/fix', json={'issue_types': issue_types})

    # Database Management API
    def get_database_tables(self) -> Dict[str, Any]:
        """Get database tables"""
        return self._make_request('GET', '/api/v1/database/tables')

    def get_table_data(self, table_name: str, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """Get table data with pagination"""
        params = {'page': page, 'page_size': page_size}
        return self._make_request('GET', f'/api/v1/database/tables/{table_name}/data', params=params)

    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """Get table schema"""
        return self._make_request('GET', f'/api/v1/database/tables/{table_name}/schema')

    def generate_table_data(self, table_name: str, count: int, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate data for table"""
        data = {'count': count}
        if options:
            data.update(options)
        return self._make_request('POST', f'/api/v1/database/tables/{table_name}/generate', json=data)

    def vacuum_database(self) -> Dict[str, Any]:
        """Vacuum database"""
        return self._make_request('POST', '/api/v1/database/vacuum')

    def analyze_database(self) -> Dict[str, Any]:
        """Analyze database"""
        return self._make_request('POST', '/api/v1/database/analyze')

    def check_database_integrity(self) -> Dict[str, Any]:
        """Check database integrity"""
        return self._make_request('GET', '/api/v1/database/integrity')

    def get_database_backups(self) -> Dict[str, Any]:
        """Get database backups"""
        return self._make_request('GET', '/api/v1/database/backups')

    def restore_database_backup(self, backup_filename: str) -> Dict[str, Any]:
        """Restore database backup"""
        return self._make_request('POST', f'/api/v1/database/restore/{backup_filename}')

    def get_database_statistics(self) -> Dict[str, Any]:
        """Get database statistics"""
        return self._make_request('GET', '/api/v1/database/statistics')

    def get_query_performance(self) -> Dict[str, Any]:
        """Get query performance statistics"""
        return self._make_request('GET', '/api/v1/database/performance')

    # Settings API
    def get_application_settings(self) -> Dict[str, Any]:
        """Get application settings"""
        return self._make_request('GET', '/api/v1/settings')

    def update_application_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Update application settings"""
        return self._make_request('POST', '/api/v1/settings', json=settings)

    def get_user_preferences(self, user_id: str = None) -> Dict[str, Any]:
        """Get user preferences"""
        params = {'user_id': user_id} if user_id else {}
        return self._make_request('GET', '/api/v1/settings/preferences', params=params)

    def update_user_preferences(self, preferences: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """Update user preferences"""
        data = preferences.copy()
        if user_id:
            data['user_id'] = user_id
        return self._make_request('POST', '/api/v1/settings/preferences', json=data)

    def reset_settings_to_defaults(self) -> Dict[str, Any]:
        """Reset settings to defaults"""
        return self._make_request('POST', '/api/v1/settings/reset')

    # System Status API
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return self._make_request('GET', '/api/v1/system/status')

    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system metrics"""
        return self._make_request('GET', '/api/v1/system/metrics')

    def get_recent_activities(self, limit: int = 10) -> Dict[str, Any]:
        """Get recent system activities"""
        params = {'limit': limit}
        return self._make_request('GET', '/api/v1/system/activities', params=params)

    def close(self):
        """Close the session"""
        self.session.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()