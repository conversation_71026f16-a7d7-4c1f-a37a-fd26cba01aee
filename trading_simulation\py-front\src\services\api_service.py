"""
API Service Module
Handles all communication with the backend API
"""

import requests
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from config.app_config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIService:
    """API service for communicating with the backend"""
    
    def __init__(self):
        self.base_url = config.api_base_url
        self.timeout = config.api_timeout
        self.endpoints = config.get_api_endpoints()
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request to API"""
        try:
            url = f"{self.base_url}{endpoint}"
            logger.info(f"Making {method} request to {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            response.raise_for_status()
            
            if response.headers.get('content-type', '').startswith('application/json'):
                return response.json()
            else:
                return {"success": True, "data": response.content}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {"success": False, "error": str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            return {"success": False, "error": "Invalid JSON response"}
    
    def get_init_config(self) -> Dict[str, Any]:
        """Get initialization configuration"""
        return self._make_request('GET', '/api/v1/init-config')
    
    def save_init_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save initialization configuration"""
        return self._make_request('POST', '/api/v1/init-config', json=config_data)
    
    def change_instrument(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Change selected instrument"""
        params = {
            'exchange': exchange,
            'symbol': symbol,
            'timeframe': timeframe
        }
        return self._make_request('POST', '/api/v1/instrument/change', params=params)
    
    def get_chart_data(self, instrument_id: int, limit: int = 1000) -> Dict[str, Any]:
        """Get chart data for instrument"""
        return self._make_request('GET', f'/api/v1/chart/data/{instrument_id}', params={'limit': limit})
    
    def get_chart_config(self, instrument_id: int) -> Dict[str, Any]:
        """Get chart configuration for instrument"""
        return self._make_request('GET', f'/api/v1/chart/config/{instrument_id}')
    
    def update_chart_config(self, instrument_id: int, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update chart configuration for instrument"""
        return self._make_request('POST', f'/api/v1/chart/config/{instrument_id}', json=config_data)
    
    def get_chart_config_by_path(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Get chart configuration by path"""
        return self._make_request('GET', f'/api/v1/chart/config/{exchange}/{symbol}/{timeframe}')
    
    def calculate_indicator(self, instrument_id: int, indicator_name: str, 
                          parameters: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Calculate technical indicator"""
        data = {
            'instrument_id': instrument_id,
            'indicator_name': indicator_name,
            'parameters': parameters
        }
        
        if options:
            data['options'] = options
            
        return self._make_request('POST', '/api/v1/indicators/calculate', json=data)
    
    def get_exchanges(self) -> Dict[str, Any]:
        """Get list of exchanges"""
        return self._make_request('GET', '/api/v1/database/exchanges')
    
    def get_instruments(self, exchange_id: Optional[int] = None) -> Dict[str, Any]:
        """Get list of instruments"""
        if exchange_id:
            return self._make_request('GET', f'/api/v1/data/instruments/{exchange_id}')
        else:
            return self._make_request('GET', '/api/instruments')
    
    def add_instrument(self, instrument_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new instrument"""
        return self._make_request('POST', '/api/v1/data/instruments', json=instrument_data)
    
    def run_simulation(self, simulation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Run trading simulation"""
        return self._make_request('POST', '/api/v1/simulation/run', json=simulation_config)
    
    def get_simulation_results(self, simulation_id: str) -> Dict[str, Any]:
        """Get simulation results"""
        return self._make_request('GET', f'/api/v1/simulation/results/{simulation_id}')
    
    def get_simulation_history(self) -> Dict[str, Any]:
        """Get simulation history"""
        return self._make_request('GET', '/api/v1/simulation/history')
    
    def get_database_status(self) -> Dict[str, Any]:
        """Get database status"""
        return self._make_request('GET', '/api/v1/database/status')
    
    def backup_database(self) -> Dict[str, Any]:
        """Backup database"""
        return self._make_request('POST', '/api/v1/database/backup')
    
    def import_csv_data(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """Import CSV data"""
        files = {'file': (filename, file_data, 'text/csv')}
        return self._make_request('POST', '/api/v1/data/import/csv', files=files)
    
    def validate_import(self, validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate import data"""
        return self._make_request('POST', '/api/v1/data/import/validate', json=validation_data)
    
    def export_data(self, exchange: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Export data"""
        return self._make_request('GET', f'/api/v1/data/export/{exchange}/{symbol}/{timeframe}')
    
    def get_methods_list(self) -> Dict[str, Any]:
        """Get list of trading methods"""
        return self._make_request('GET', '/api/v1/methods/list')
    
    def get_method_details(self, method_id: int) -> Dict[str, Any]:
        """Get method details"""
        return self._make_request('GET', f'/api/v1/methods/{method_id}')
    
    def get_method_submethods(self, method_id: int) -> Dict[str, Any]:
        """Get method submethods"""
        return self._make_request('GET', f'/api/v1/methods/{method_id}/submethods')
    
    def update_method_config(self, method_id: int, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update method configuration"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/config', json=config_data)
    
    def test_method(self, method_id: int) -> Dict[str, Any]:
        """Test method"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/test')
    
    def optimize_method(self, method_id: int, optimization_config: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize method"""
        return self._make_request('POST', f'/api/v1/methods/{method_id}/optimize', json=optimization_config)
    
    def health_check(self) -> Dict[str, Any]:
        """Health check endpoint"""
        return self._make_request('GET', '/health')
    
    def close(self):
        """Close the session"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close() 