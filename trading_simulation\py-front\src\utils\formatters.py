"""
Formatting Utilities
Helper functions for formatting data for display
"""

from typing import Any, Optional, Union
from datetime import datetime, timedelta
import locale

def format_currency(
    value: Union[int, float], 
    currency: str = "USD", 
    decimal_places: int = 2
) -> str:
    """
    Format value as currency
    
    Args:
        value: Numeric value
        currency: Currency code
        decimal_places: Number of decimal places
        
    Returns:
        Formatted currency string
    """
    if value is None:
        return "N/A"
    
    try:
        if currency == "USD":
            return f"${value:,.{decimal_places}f}"
        elif currency == "EUR":
            return f"€{value:,.{decimal_places}f}"
        elif currency == "GBP":
            return f"£{value:,.{decimal_places}f}"
        else:
            return f"{value:,.{decimal_places}f} {currency}"
    except (ValueError, TypeError):
        return "N/A"

def format_percentage(
    value: Union[int, float], 
    decimal_places: int = 1,
    include_sign: bool = True
) -> str:
    """
    Format value as percentage
    
    Args:
        value: Numeric value
        decimal_places: Number of decimal places
        include_sign: Whether to include + sign for positive values
        
    Returns:
        Formatted percentage string
    """
    if value is None:
        return "N/A"
    
    try:
        formatted = f"{value:.{decimal_places}f}%"
        if include_sign and value > 0:
            formatted = f"+{formatted}"
        return formatted
    except (ValueError, TypeError):
        return "N/A"

def format_number(
    value: Union[int, float], 
    decimal_places: int = 2,
    use_thousands_separator: bool = True
) -> str:
    """
    Format number with optional thousands separator
    
    Args:
        value: Numeric value
        decimal_places: Number of decimal places
        use_thousands_separator: Whether to use thousands separator
        
    Returns:
        Formatted number string
    """
    if value is None:
        return "N/A"
    
    try:
        if use_thousands_separator:
            return f"{value:,.{decimal_places}f}"
        else:
            return f"{value:.{decimal_places}f}"
    except (ValueError, TypeError):
        return "N/A"

def format_large_number(value: Union[int, float]) -> str:
    """
    Format large numbers with K, M, B suffixes
    
    Args:
        value: Numeric value
        
    Returns:
        Formatted number string with suffix
    """
    if value is None:
        return "N/A"
    
    try:
        abs_value = abs(value)
        sign = "-" if value < 0 else ""
        
        if abs_value >= 1_000_000_000:
            return f"{sign}{abs_value/1_000_000_000:.1f}B"
        elif abs_value >= 1_000_000:
            return f"{sign}{abs_value/1_000_000:.1f}M"
        elif abs_value >= 1_000:
            return f"{sign}{abs_value/1_000:.1f}K"
        else:
            return f"{sign}{abs_value:.0f}"
    except (ValueError, TypeError):
        return "N/A"

def format_datetime(
    dt: Union[datetime, str], 
    format_string: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """
    Format datetime for display
    
    Args:
        dt: Datetime object or string
        format_string: Format string
        
    Returns:
        Formatted datetime string
    """
    if dt is None:
        return "N/A"
    
    try:
        if isinstance(dt, str):
            # Try to parse common datetime formats
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%Y-%m-%dT%H:%M:%S"]:
                try:
                    dt = datetime.strptime(dt, fmt)
                    break
                except ValueError:
                    continue
            else:
                return dt  # Return original string if parsing fails
        
        if isinstance(dt, datetime):
            return dt.strftime(format_string)
        
        return str(dt)
    except (ValueError, TypeError):
        return "N/A"

def format_time_ago(dt: Union[datetime, str]) -> str:
    """
    Format datetime as time ago (e.g., "2 hours ago")
    
    Args:
        dt: Datetime object or string
        
    Returns:
        Time ago string
    """
    if dt is None:
        return "N/A"
    
    try:
        if isinstance(dt, str):
            # Try to parse datetime string
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%Y-%m-%dT%H:%M:%S"]:
                try:
                    dt = datetime.strptime(dt, fmt)
                    break
                except ValueError:
                    continue
            else:
                return dt  # Return original string if parsing fails
        
        if not isinstance(dt, datetime):
            return str(dt)
        
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "Just now"
    
    except (ValueError, TypeError):
        return "N/A"

def format_duration(seconds: Union[int, float]) -> str:
    """
    Format duration in seconds to human readable format
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds is None:
        return "N/A"
    
    try:
        seconds = int(seconds)
        
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            if remaining_seconds > 0:
                return f"{minutes}m {remaining_seconds}s"
            else:
                return f"{minutes}m"
        else:
            hours = seconds // 3600
            remaining_minutes = (seconds % 3600) // 60
            if remaining_minutes > 0:
                return f"{hours}h {remaining_minutes}m"
            else:
                return f"{hours}h"
    
    except (ValueError, TypeError):
        return "N/A"

def format_file_size(size_bytes: Union[int, float]) -> str:
    """
    Format file size in bytes to human readable format
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted file size string
    """
    if size_bytes is None:
        return "N/A"
    
    try:
        size_bytes = float(size_bytes)
        
        if size_bytes < 1024:
            return f"{size_bytes:.0f} B"
        elif size_bytes < 1024 ** 2:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 ** 3:
            return f"{size_bytes / (1024 ** 2):.1f} MB"
        else:
            return f"{size_bytes / (1024 ** 3):.1f} GB"
    
    except (ValueError, TypeError):
        return "N/A"

def format_metric_value(
    value: Any, 
    metric_type: str = "number",
    **kwargs
) -> str:
    """
    Format metric value based on type
    
    Args:
        value: Value to format
        metric_type: Type of metric (number, currency, percentage, time, size)
        **kwargs: Additional formatting options
        
    Returns:
        Formatted metric value
    """
    if value is None:
        return "N/A"
    
    try:
        if metric_type == "currency":
            return format_currency(value, **kwargs)
        elif metric_type == "percentage":
            return format_percentage(value, **kwargs)
        elif metric_type == "time":
            return format_duration(value)
        elif metric_type == "size":
            return format_file_size(value)
        elif metric_type == "large_number":
            return format_large_number(value)
        else:
            return format_number(value, **kwargs)
    
    except (ValueError, TypeError):
        return "N/A"

def format_status_badge(status: str) -> str:
    """
    Format status as badge with emoji
    
    Args:
        status: Status string
        
    Returns:
        Formatted status badge
    """
    status_map = {
        "active": "🟢 Active",
        "inactive": "🔴 Inactive",
        "running": "🟡 Running",
        "stopped": "⚫ Stopped",
        "success": "✅ Success",
        "error": "❌ Error",
        "warning": "⚠️ Warning",
        "info": "ℹ️ Info",
        "pending": "🟡 Pending",
        "completed": "✅ Completed",
        "failed": "❌ Failed",
        "cancelled": "⚫ Cancelled",
    }
    
    return status_map.get(status.lower(), f"🔵 {status.title()}")

def format_trading_signal(signal: str) -> str:
    """
    Format trading signal with appropriate emoji
    
    Args:
        signal: Signal string (buy, sell, hold)
        
    Returns:
        Formatted signal string
    """
    signal_map = {
        "buy": "🟢 BUY",
        "sell": "🔴 SELL",
        "hold": "🟡 HOLD",
        "long": "🟢 LONG",
        "short": "🔴 SHORT",
        "neutral": "🟡 NEUTRAL",
    }
    
    return signal_map.get(signal.lower(), f"🔵 {signal.upper()}")

def format_price_change(current: float, previous: float) -> str:
    """
    Format price change with color indication
    
    Args:
        current: Current price
        previous: Previous price
        
    Returns:
        Formatted price change string
    """
    if current is None or previous is None or previous == 0:
        return "N/A"
    
    try:
        change = current - previous
        change_percent = (change / previous) * 100
        
        if change > 0:
            return f"🟢 +{change:.2f} (+{change_percent:.2f}%)"
        elif change < 0:
            return f"🔴 {change:.2f} ({change_percent:.2f}%)"
        else:
            return f"🟡 0.00 (0.00%)"
    
    except (ValueError, TypeError, ZeroDivisionError):
        return "N/A"

def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    Truncate text to maximum length
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncated
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def format_table_cell(value: Any, column_type: str = "text") -> str:
    """
    Format table cell value based on column type
    
    Args:
        value: Cell value
        column_type: Type of column (text, number, currency, percentage, datetime, status)
        
    Returns:
        Formatted cell value
    """
    if value is None:
        return ""
    
    try:
        if column_type == "currency":
            return format_currency(value)
        elif column_type == "percentage":
            return format_percentage(value)
        elif column_type == "number":
            return format_number(value)
        elif column_type == "datetime":
            return format_datetime(value)
        elif column_type == "status":
            return format_status_badge(str(value))
        else:
            return str(value)
    
    except (ValueError, TypeError):
        return str(value) if value is not None else ""
