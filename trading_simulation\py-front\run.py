#!/usr/bin/env python3
"""
Startup script for Trading Simulation Python Frontend
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """Run the Streamlit application"""
    
    # Get the directory of this script
    script_dir = Path(__file__).parent
    app_path = script_dir / "app.py"
    
    # Change to the script directory
    os.chdir(script_dir)
    
    # Run streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run", str(app_path),
        "--server.port", "8501",
        "--server.address", "localhost",
        "--browser.gatherUsageStats", "false"
    ]
    
    print("Starting Trading Simulation Frontend...")
    print(f"Command: {' '.join(cmd)}")
    print("Access the application at: http://localhost:8501")
    print("Press Ctrl+C to stop the server")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nShutting down...")
    except subprocess.CalledProcessError as e:
        print(f"Error running Streamlit: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
