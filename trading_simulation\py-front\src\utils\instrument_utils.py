"""
Instrument Utilities
Helper functions for instrument management and validation
"""

from typing import Dict, List, Any, Optional, <PERSON>ple
import re
from datetime import datetime, timed<PERSON>ta

def validate_instrument_data(instrument: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate instrument data
    
    Args:
        instrument: Instrument data dictionary
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Required fields
    required_fields = ['exchange', 'symbol']
    for field in required_fields:
        if not instrument.get(field):
            errors.append(f"Missing required field: {field}")
    
    # Validate symbol format
    symbol = instrument.get('symbol', '')
    if symbol and not is_valid_symbol(symbol):
        errors.append(f"Invalid symbol format: {symbol}")
    
    # Validate exchange
    exchange = instrument.get('exchange', '')
    if exchange and not is_valid_exchange(exchange):
        errors.append(f"Invalid exchange: {exchange}")
    
    # Validate timeframe if provided
    timeframe = instrument.get('timeframe')
    if timeframe and not is_valid_timeframe(timeframe):
        errors.append(f"Invalid timeframe: {timeframe}")
    
    return len(errors) == 0, errors

def is_valid_symbol(symbol: str) -> bool:
    """
    Check if symbol format is valid
    
    Args:
        symbol: Trading symbol
        
    Returns:
        True if valid, False otherwise
    """
    if not symbol or len(symbol) < 2:
        return False
    
    # Allow alphanumeric characters, hyphens, and underscores
    pattern = r'^[A-Z0-9_-]+$'
    return bool(re.match(pattern, symbol.upper()))

def is_valid_exchange(exchange: str) -> bool:
    """
    Check if exchange name is valid
    
    Args:
        exchange: Exchange name
        
    Returns:
        True if valid, False otherwise
    """
    if not exchange or len(exchange) < 2:
        return False
    
    # Allow alphanumeric characters and spaces
    pattern = r'^[A-Za-z0-9\s]+$'
    return bool(re.match(pattern, exchange))

def is_valid_timeframe(timeframe: str) -> bool:
    """
    Check if timeframe is valid
    
    Args:
        timeframe: Timeframe string (e.g., '1m', '5m', '1h', '1d')
        
    Returns:
        True if valid, False otherwise
    """
    valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
    return timeframe in valid_timeframes

def format_symbol_display(symbol: str, exchange: str = None) -> str:
    """
    Format symbol for display
    
    Args:
        symbol: Trading symbol
        exchange: Exchange name (optional)
        
    Returns:
        Formatted symbol string
    """
    if not symbol:
        return "Unknown Symbol"
    
    formatted_symbol = symbol.upper()
    
    if exchange:
        return f"{exchange}:{formatted_symbol}"
    
    return formatted_symbol

def parse_symbol_pair(symbol: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Parse trading pair symbol into base and quote currencies
    
    Args:
        symbol: Trading symbol (e.g., 'BTCUSDT', 'BTC-USD')
        
    Returns:
        Tuple of (base_currency, quote_currency)
    """
    if not symbol:
        return None, None
    
    symbol = symbol.upper()
    
    # Common separators
    separators = ['-', '_', '/']
    
    for sep in separators:
        if sep in symbol:
            parts = symbol.split(sep)
            if len(parts) == 2:
                return parts[0], parts[1]
    
    # Try to parse common patterns
    common_quotes = ['USDT', 'BUSD', 'USD', 'EUR', 'BTC', 'ETH', 'BNB']
    
    for quote in common_quotes:
        if symbol.endswith(quote) and len(symbol) > len(quote):
            base = symbol[:-len(quote)]
            return base, quote
    
    return symbol, None

def get_instrument_display_name(instrument: Dict[str, Any]) -> str:
    """
    Get display name for instrument
    
    Args:
        instrument: Instrument data
        
    Returns:
        Display name string
    """
    symbol = instrument.get('symbol', 'Unknown')
    exchange = instrument.get('exchange', '')
    name = instrument.get('name', '')
    
    if name:
        return f"{symbol} ({name})"
    elif exchange:
        return f"{exchange}:{symbol}"
    else:
        return symbol

def filter_instruments(
    instruments: List[Dict[str, Any]], 
    search_term: str = None,
    exchange_filter: str = None,
    symbol_filter: str = None
) -> List[Dict[str, Any]]:
    """
    Filter instruments based on criteria
    
    Args:
        instruments: List of instruments
        search_term: Search term for symbol or name
        exchange_filter: Filter by exchange
        symbol_filter: Filter by symbol pattern
        
    Returns:
        Filtered list of instruments
    """
    filtered = instruments.copy()
    
    # Apply search term filter
    if search_term:
        search_term = search_term.lower()
        filtered = [
            inst for inst in filtered
            if (search_term in inst.get('symbol', '').lower() or
                search_term in inst.get('name', '').lower() or
                search_term in inst.get('exchange', '').lower())
        ]
    
    # Apply exchange filter
    if exchange_filter and exchange_filter != 'All':
        filtered = [
            inst for inst in filtered
            if inst.get('exchange', '').lower() == exchange_filter.lower()
        ]
    
    # Apply symbol filter
    if symbol_filter:
        symbol_pattern = symbol_filter.lower()
        filtered = [
            inst for inst in filtered
            if symbol_pattern in inst.get('symbol', '').lower()
        ]
    
    return filtered

def sort_instruments(
    instruments: List[Dict[str, Any]], 
    sort_by: str = 'symbol',
    ascending: bool = True
) -> List[Dict[str, Any]]:
    """
    Sort instruments by specified field
    
    Args:
        instruments: List of instruments
        sort_by: Field to sort by ('symbol', 'exchange', 'name', 'volume')
        ascending: Sort order
        
    Returns:
        Sorted list of instruments
    """
    if not instruments:
        return instruments
    
    def get_sort_key(instrument: Dict[str, Any]) -> Any:
        value = instrument.get(sort_by, '')
        
        # Handle numeric fields
        if sort_by in ['volume', 'price', 'market_cap']:
            try:
                return float(value) if value else 0
            except (ValueError, TypeError):
                return 0
        
        # Handle string fields
        return str(value).lower() if value else ''
    
    return sorted(instruments, key=get_sort_key, reverse=not ascending)

def get_instrument_by_id(instruments: List[Dict[str, Any]], instrument_id: int) -> Optional[Dict[str, Any]]:
    """
    Get instrument by ID
    
    Args:
        instruments: List of instruments
        instrument_id: Instrument ID
        
    Returns:
        Instrument data or None if not found
    """
    for instrument in instruments:
        if instrument.get('id') == instrument_id or instrument.get('instrument_id') == instrument_id:
            return instrument
    return None

def get_instrument_by_symbol(
    instruments: List[Dict[str, Any]], 
    symbol: str, 
    exchange: str = None
) -> Optional[Dict[str, Any]]:
    """
    Get instrument by symbol and optionally exchange
    
    Args:
        instruments: List of instruments
        symbol: Trading symbol
        exchange: Exchange name (optional)
        
    Returns:
        Instrument data or None if not found
    """
    for instrument in instruments:
        if instrument.get('symbol', '').upper() == symbol.upper():
            if exchange is None or instrument.get('exchange', '').upper() == exchange.upper():
                return instrument
    return None

def create_instrument_key(exchange: str, symbol: str, timeframe: str = None) -> str:
    """
    Create unique key for instrument
    
    Args:
        exchange: Exchange name
        symbol: Trading symbol
        timeframe: Timeframe (optional)
        
    Returns:
        Unique instrument key
    """
    key = f"{exchange.upper()}:{symbol.upper()}"
    if timeframe:
        key += f":{timeframe}"
    return key

def parse_instrument_key(key: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Parse instrument key into components
    
    Args:
        key: Instrument key (e.g., 'BINANCE:BTCUSDT:1d')
        
    Returns:
        Tuple of (exchange, symbol, timeframe)
    """
    if not key:
        return None, None, None
    
    parts = key.split(':')
    
    if len(parts) >= 2:
        exchange = parts[0]
        symbol = parts[1]
        timeframe = parts[2] if len(parts) > 2 else None
        return exchange, symbol, timeframe
    
    return None, None, None

def get_current_instrument_id(app_state) -> Optional[int]:
    """
    Get current instrument ID from app state
    
    Args:
        app_state: Application state object
        
    Returns:
        Current instrument ID or None
    """
    if hasattr(app_state, 'selected_instrument') and app_state.selected_instrument:
        return app_state.selected_instrument.get('instrument_id') or app_state.selected_instrument.get('id')
    return None

def is_current_instrument_available(app_state) -> bool:
    """
    Check if current instrument is available
    
    Args:
        app_state: Application state object
        
    Returns:
        True if instrument is available, False otherwise
    """
    return get_current_instrument_id(app_state) is not None

def format_timeframe_display(timeframe: str) -> str:
    """
    Format timeframe for display
    
    Args:
        timeframe: Timeframe string
        
    Returns:
        Formatted timeframe string
    """
    timeframe_map = {
        '1m': '1 Minute',
        '5m': '5 Minutes',
        '15m': '15 Minutes',
        '30m': '30 Minutes',
        '1h': '1 Hour',
        '2h': '2 Hours',
        '4h': '4 Hours',
        '6h': '6 Hours',
        '8h': '8 Hours',
        '12h': '12 Hours',
        '1d': '1 Day',
        '3d': '3 Days',
        '1w': '1 Week',
        '1M': '1 Month',
    }
    
    return timeframe_map.get(timeframe, timeframe)

def get_timeframe_minutes(timeframe: str) -> int:
    """
    Get timeframe in minutes
    
    Args:
        timeframe: Timeframe string
        
    Returns:
        Number of minutes
    """
    timeframe_minutes = {
        '1m': 1,
        '5m': 5,
        '15m': 15,
        '30m': 30,
        '1h': 60,
        '2h': 120,
        '4h': 240,
        '6h': 360,
        '8h': 480,
        '12h': 720,
        '1d': 1440,
        '3d': 4320,
        '1w': 10080,
        '1M': 43200,  # Approximate
    }
    
    return timeframe_minutes.get(timeframe, 60)  # Default to 1 hour
