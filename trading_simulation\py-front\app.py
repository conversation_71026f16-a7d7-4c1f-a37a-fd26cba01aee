#!/usr/bin/env python3
"""
Trading Simulation Python Frontend
Main application entry point using Streamlit
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.append(str(src_path))

# Import application modules
from pages.main_dashboard import main_dashboard_page
from pages.chart_page import chart_page
from pages.simulation_page import simulation_page
from pages.settings_page import settings_page
from config.app_config import AppConfig
from services.api_service import APIService
from store.app_state import AppState

def main():
    """Main application entry point"""
    
    # Configure Streamlit page
    st.set_page_config(
        page_title="Trading Simulation",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize application state
    if 'app_state' not in st.session_state:
        st.session_state.app_state = AppState()
    
    # Initialize API service
    if 'api_service' not in st.session_state:
        st.session_state.api_service = APIService()
    
    # Load configuration
    config = AppConfig()
    
    # Sidebar navigation
    st.sidebar.title("Trading Simulation")
    
    # Navigation menu
    page = st.sidebar.selectbox(
        "Navigation",
        ["Dashboard", "Chart View", "Simulation", "Settings"],
        index=0
    )
    
    # Page routing
    if page == "Dashboard":
        main_dashboard_page()
    elif page == "Chart View":
        chart_page()
    elif page == "Simulation":
        simulation_page()
    elif page == "Settings":
        settings_page()
    
    # Footer
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Version:** 1.0.0")
    st.sidebar.markdown("**Backend:** Python FastAPI")
    st.sidebar.markdown("**Frontend:** Streamlit")

if __name__ == "__main__":
    main() 