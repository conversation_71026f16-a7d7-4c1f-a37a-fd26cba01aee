"""
Main Dashboard Page
Home page for the trading simulation application
"""

import streamlit as st
from typing import Dict, Any
from services.api_service import APIService
from store.app_state import AppState

def main_dashboard_page():
    """Main dashboard page"""
    
    st.title("📈 Trading Simulation Dashboard")
    st.markdown("---")
    
    # Get application state
    app_state: AppState = st.session_state.app_state
    api_service: APIService = st.session_state.api_service
    
    # Dashboard overview
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="Total Exchanges",
            value=len(app_state.exchanges),
            delta="0"
        )
    
    with col2:
        st.metric(
            label="Total Instruments",
            value=len(app_state.instruments),
            delta="0"
        )
    
    with col3:
        st.metric(
            label="Active Indicators",
            value=len([ind for ind in app_state.indicators if ind.enabled]),
            delta="0"
        )
    
    with col4:
        st.metric(
            label="Data Points",
            value=len(app_state.chart_data),
            delta="0"
        )
    
    # Quick actions
    st.subheader("🚀 Quick Actions")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 Open Chart View", use_container_width=True):
            st.switch_page("pages/chart_page.py")
    
    with col2:
        if st.button("🎯 Run Simulation", use_container_width=True):
            st.switch_page("pages/simulation_page.py")
    
    with col3:
        if st.button("⚙️ Settings", use_container_width=True):
            st.switch_page("pages/settings_page.py")
    
    # Recent activity
    st.subheader("📋 Recent Activity")
    
    # Placeholder for recent activity
    st.info("No recent activity to display")
    
    # System status
    st.subheader("🔧 System Status")
    
    # Check backend connectivity
    if st.button("Check Backend Status"):
        with st.spinner("Checking backend status..."):
            try:
                response = api_service.health_check()
                if response.get("success"):
                    st.success("✅ Backend is running")
                else:
                    st.error("❌ Backend is not responding")
            except Exception as e:
                st.error(f"❌ Error connecting to backend: {str(e)}")
    
    # Database status
    if st.button("Check Database Status"):
        with st.spinner("Checking database status..."):
            try:
                response = api_service.get_database_status()
                if response.get("success"):
                    st.success("✅ Database is connected")
                else:
                    st.error("❌ Database connection failed")
            except Exception as e:
                st.error(f"❌ Error checking database: {str(e)}")
    
    # Quick data import
    st.subheader("📥 Quick Data Import")
    
    uploaded_file = st.file_uploader(
        "Upload CSV file",
        type=['csv'],
        help="Upload market data CSV file"
    )
    
    if uploaded_file is not None:
        if st.button("Import Data"):
            with st.spinner("Importing data..."):
                try:
                    file_data = uploaded_file.read()
                    response = api_service.import_csv_data(file_data, uploaded_file.name)
                    if response.get("success"):
                        st.success("✅ Data imported successfully")
                    else:
                        st.error(f"❌ Import failed: {response.get('error', 'Unknown error')}")
                except Exception as e:
                    st.error(f"❌ Error importing data: {str(e)}")
    
    # Footer
    st.markdown("---")
    st.markdown("**Trading Simulation v1.0.0** | Built with Streamlit") 