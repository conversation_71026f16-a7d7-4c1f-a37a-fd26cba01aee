"""
Main Dashboard Page
Home page for the trading simulation application
"""

import streamlit as st
from typing import Dict, Any
from services.api_service import APIService
from store.app_state import AppState

def main_dashboard_page():
    """Main dashboard page"""

    st.title("📈 Trading Simulation Dashboard")
    st.markdown("Welcome to the Trading Simulation System")

    # Get application state
    app_state: AppState = st.session_state.app_state
    api_service: APIService = st.session_state.api_service

    # Instrument Selection Section (moved from Chart page since it affects other parts)
    st.subheader("🎯 Instrument Selection")

    col1, col2, col3 = st.columns(3)

    with col1:
        # Exchange selection
        exchanges = load_exchanges(api_service)
        exchange_names = [ex.get('name', 'Unknown') for ex in exchanges]

        if exchange_names:
            selected_exchange = st.selectbox(
                "Exchange",
                exchange_names,
                index=0 if not app_state.selected_exchange else
                      exchange_names.index(app_state.selected_exchange) if app_state.selected_exchange in exchange_names else 0
            )
            app_state.selected_exchange = selected_exchange
        else:
            st.warning("No exchanges available")
            selected_exchange = None

    with col2:
        # Symbol selection
        if selected_exchange:
            instruments = load_instruments(api_service, selected_exchange)
            symbol_names = [inst.get('symbol', 'Unknown') for inst in instruments]

            if symbol_names:
                selected_symbol = st.selectbox(
                    "Symbol",
                    symbol_names,
                    index=0 if not app_state.selected_symbol else
                          symbol_names.index(app_state.selected_symbol) if app_state.selected_symbol in symbol_names else 0
                )
                app_state.selected_symbol = selected_symbol

                # Update selected instrument
                selected_instrument = next((inst for inst in instruments if inst.get('symbol') == selected_symbol), None)
                app_state.selected_instrument = selected_instrument
            else:
                st.warning("No instruments available for this exchange")
        else:
            st.selectbox("Symbol", [], disabled=True, placeholder="Select exchange first")

    with col3:
        # Timeframe selection
        timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
        selected_timeframe = st.selectbox(
            "Timeframe",
            timeframes,
            index=timeframes.index(app_state.selected_timeframe) if app_state.selected_timeframe in timeframes else 5
        )
        app_state.selected_timeframe = selected_timeframe

    # Display current selection
    if app_state.selected_instrument:
        st.success(f"✅ Selected: {selected_exchange} - {selected_symbol} ({selected_timeframe})")
    else:
        st.info("Please select an instrument to enable other features")

    st.markdown("---")

    # Dashboard Metrics
    st.subheader("📊 System Overview")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        active_instruments = 1 if app_state.selected_instrument else 0
        st.metric(
            label="Active Instruments",
            value=active_instruments,
            delta="0"
        )

    with col2:
        total_methods = get_total_methods(api_service, app_state)
        st.metric(
            label="Total Methods",
            value=total_methods,
            delta="0"
        )

    with col3:
        data_points = get_data_points_count(api_service, app_state)
        st.metric(
            label="Data Points",
            value=data_points,
            delta="0"
        )

    with col4:
        success_rate = get_success_rate(api_service, app_state)
        st.metric(
            label="Success Rate",
            value=f"{success_rate:.1f}%",
            delta="0"
        )
    
    # System Status
    st.subheader("🔧 System Status")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        database_status = check_database_status(api_service)
        status_color = "🟢" if database_status else "🔴"
        st.markdown(f"**Database:** {status_color}")

    with col2:
        api_status = check_api_status(api_service)
        status_color = "🟢" if api_status else "🔴"
        st.markdown(f"**API:** {status_color}")

    with col3:
        data_feed_status = check_data_feed_status(api_service)
        status_color = "🟢" if data_feed_status else "🔴"
        st.markdown(f"**Data Feed:** {status_color}")

    with col4:
        simulation_status = check_simulation_status(api_service)
        status_color = "🟢" if simulation_status else "🔴"
        st.markdown(f"**Simulation:** {status_color}")

    # Quick actions
    st.subheader("🚀 Quick Actions")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🎯 Configure Methods", use_container_width=True):
            st.switch_page("pages/methods_page.py")

    with col2:
        if st.button("🚀 Run Simulation", use_container_width=True):
            st.switch_page("pages/simulation_page.py")

    with col3:
        if st.button("📊 Manage Data", use_container_width=True):
            st.switch_page("pages/data_page.py")

    with col4:
        if st.button("⚙️ Settings", use_container_width=True):
            st.switch_page("pages/settings_page.py")
    
    # Recent activity
    st.subheader("📋 Recent Activity")
    
    # Placeholder for recent activity
    st.info("No recent activity to display")
    
    # System status
    st.subheader("🔧 System Status")
    
    # Check backend connectivity
    if st.button("Check Backend Status"):
        with st.spinner("Checking backend status..."):
            try:
                response = api_service.health_check()
                if response.get("success"):
                    st.success("✅ Backend is running")
                else:
                    st.error("❌ Backend is not responding")
            except Exception as e:
                st.error(f"❌ Error connecting to backend: {str(e)}")
    
    # Database status
    if st.button("Check Database Status"):
        with st.spinner("Checking database status..."):
            try:
                response = api_service.get_database_status()
                if response.get("success"):
                    st.success("✅ Database is connected")
                else:
                    st.error("❌ Database connection failed")
            except Exception as e:
                st.error(f"❌ Error checking database: {str(e)}")
    
    # Quick data import
    st.subheader("📥 Quick Data Import")
    
    uploaded_file = st.file_uploader(
        "Upload CSV file",
        type=['csv'],
        help="Upload market data CSV file"
    )
    
    if uploaded_file is not None:
        if st.button("Import Data"):
            with st.spinner("Importing data..."):
                try:
                    file_data = uploaded_file.read()
                    response = api_service.import_csv_data(file_data, uploaded_file.name)
                    if response.get("success"):
                        st.success("✅ Data imported successfully")
                    else:
                        st.error(f"❌ Import failed: {response.get('error', 'Unknown error')}")
                except Exception as e:
                    st.error(f"❌ Error importing data: {str(e)}")
    
    # Recent Activity
    st.subheader("📋 Recent Activity")

    # Display recent activity (placeholder for now)
    recent_activities = get_recent_activities(api_service)

    if recent_activities:
        for activity in recent_activities[:5]:  # Show last 5 activities
            activity_type = activity.get('type', 'system')
            message = activity.get('message', 'Unknown activity')
            time = activity.get('time', 'Unknown time')
            status = activity.get('status', 'info')

            status_icon = {
                'success': '✅',
                'warning': '⚠️',
                'error': '❌',
                'info': 'ℹ️'
            }.get(status, 'ℹ️')

            st.markdown(f"{status_icon} **{activity_type.title()}:** {message} _{time}_")
    else:
        st.info("No recent activity to display")

    # Footer
    st.markdown("---")
    st.markdown("**Trading Simulation v1.0.0** | Built with Streamlit")


# Helper Functions

def load_exchanges(api_service: APIService) -> list:
    """Load available exchanges"""
    try:
        # This would be an actual API call
        # For now, return placeholder data
        return [
            {'id': 1, 'name': 'Binance'},
            {'id': 2, 'name': 'Coinbase'},
            {'id': 3, 'name': 'Kraken'}
        ]
    except Exception as e:
        st.error(f"Failed to load exchanges: {str(e)}")
        return []

def load_instruments(api_service: APIService, exchange: str) -> list:
    """Load instruments for a specific exchange"""
    try:
        # This would be an actual API call
        # For now, return placeholder data based on exchange
        instruments_map = {
            'Binance': [
                {'id': 1, 'symbol': 'BTCUSDT', 'exchange': 'Binance'},
                {'id': 2, 'symbol': 'ETHUSDT', 'exchange': 'Binance'},
                {'id': 3, 'symbol': 'ADAUSDT', 'exchange': 'Binance'}
            ],
            'Coinbase': [
                {'id': 4, 'symbol': 'BTC-USD', 'exchange': 'Coinbase'},
                {'id': 5, 'symbol': 'ETH-USD', 'exchange': 'Coinbase'}
            ],
            'Kraken': [
                {'id': 6, 'symbol': 'XBTUSD', 'exchange': 'Kraken'},
                {'id': 7, 'symbol': 'ETHUSD', 'exchange': 'Kraken'}
            ]
        }
        return instruments_map.get(exchange, [])
    except Exception as e:
        st.error(f"Failed to load instruments: {str(e)}")
        return []

def get_total_methods(api_service: APIService, app_state: AppState) -> int:
    """Get total number of trading methods"""
    try:
        # This would be an actual API call
        return 12  # Placeholder
    except Exception:
        return 0

def get_data_points_count(api_service: APIService, app_state: AppState) -> int:
    """Get total number of data points"""
    try:
        # This would be an actual API call
        return 1500  # Placeholder
    except Exception:
        return 0

def get_success_rate(api_service: APIService, app_state: AppState) -> float:
    """Get overall success rate"""
    try:
        # This would be an actual API call
        return 67.5  # Placeholder
    except Exception:
        return 0.0

def check_database_status(api_service: APIService) -> bool:
    """Check database connectivity status"""
    try:
        # This would be an actual API call
        return True  # Placeholder
    except Exception:
        return False

def check_api_status(api_service: APIService) -> bool:
    """Check API status"""
    try:
        # This would be an actual API call
        return True  # Placeholder
    except Exception:
        return False

def check_data_feed_status(api_service: APIService) -> bool:
    """Check data feed status"""
    try:
        # This would be an actual API call
        return True  # Placeholder
    except Exception:
        return False

def check_simulation_status(api_service: APIService) -> bool:
    """Check simulation engine status"""
    try:
        # This would be an actual API call
        return True  # Placeholder
    except Exception:
        return False

def get_recent_activities(api_service: APIService) -> list:
    """Get recent system activities"""
    try:
        # This would be an actual API call
        # For now, return placeholder data
        return [
            {
                'type': 'simulation',
                'message': 'Simulation completed for BTCUSDT',
                'time': '2 minutes ago',
                'status': 'success'
            },
            {
                'type': 'data',
                'message': 'Market data updated',
                'time': '5 minutes ago',
                'status': 'success'
            },
            {
                'type': 'method',
                'message': 'RSI method configuration updated',
                'time': '10 minutes ago',
                'status': 'info'
            }
        ]
    except Exception:
        return []