"""
Application Configuration Module
Handles all configuration settings for the Python frontend
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class AppConfig(BaseSettings):
    """Application configuration settings"""
    
    # API Configuration
    api_base_url: str = Field(
        default="http://localhost:8000",
        env="API_BASE_URL"
    )
    api_timeout: int = Field(
        default=30,
        env="API_TIMEOUT"
    )
    
    # Chart Configuration
    chart_width: int = Field(default=800)
    chart_height: int = Field(default=600)
    chart_theme: str = Field(default="light")
    chart_refresh_interval: int = Field(default=5000)
    
    # Data Configuration
    default_limit: int = Field(default=1000)
    max_data_points: int = Field(default=10000)
    
    # UI Configuration
    sidebar_width: int = Field(default=300)
    page_title: str = Field(default="Trading Simulation")
    page_icon: str = Field(default="📈")
    
    # Development Configuration
    debug: bool = Field(
        default=False,
        env="DEBUG"
    )
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL"
    )
    
    # Trading Configuration
    default_risk_per_trade: float = Field(default=2.0)
    default_stop_loss: float = Field(default=5.0)
    default_take_profit: float = Field(default=10.0)
    max_concurrent_positions: int = Field(default=5)
    default_commission: float = Field(default=5.0)
    default_slippage: float = Field(default=0.1)

    # Simulation Configuration
    max_simulation_history: int = Field(default=100)
    simulation_timeout: int = Field(default=300)  # 5 minutes

    # Database Configuration
    database_url: str = Field(
        default="sqlite:///trading_simulation.db",
        env="DATABASE_URL"
    )
    backup_frequency: str = Field(default="daily")
    max_backup_files: int = Field(default=10)
    vacuum_frequency: str = Field(default="weekly")
    enable_wal_mode: bool = Field(default=True)

    # Data Management Configuration
    max_file_size_mb: int = Field(default=100)
    allowed_file_types: list = Field(default=["csv", "json", "xlsx"])
    data_cache_ttl: int = Field(default=300)  # 5 minutes
    max_cache_size: int = Field(default=1000)

    # Security Configuration
    enable_authentication: bool = Field(default=False)
    session_timeout: int = Field(default=60)  # minutes
    max_login_attempts: int = Field(default=3)
    enable_api_key: bool = Field(default=False)
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    encrypt_sensitive_data: bool = Field(default=True)

    # Notification Configuration
    enable_email_notifications: bool = Field(default=False)
    email_smtp_server: Optional[str] = Field(default=None, env="SMTP_SERVER")
    email_smtp_port: int = Field(default=587, env="SMTP_PORT")
    email_username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    email_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    enable_browser_notifications: bool = Field(default=True)

    # Performance Configuration
    enable_parallel_processing: bool = Field(default=True)
    max_workers: int = Field(default=4)

    # ChartDirector Configuration (when available)
    chartdirector_enabled: bool = Field(default=False)
    chartdirector_license: Optional[str] = Field(
        default=None,
        env="CHARTDIRECTOR_LICENSE"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def get_api_endpoints(self) -> Dict[str, str]:
        """Get API endpoint URLs"""
        return {
            # Core endpoints
            "init_config": f"{self.api_base_url}/api/v1/init-config",
            "health": f"{self.api_base_url}/health",

            # Chart endpoints (for compatibility)
            "chart_data": f"{self.api_base_url}/api/v1/chart/data",
            "chart_config": f"{self.api_base_url}/api/v1/chart/config",
            "indicators": f"{self.api_base_url}/api/v1/indicators/calculate",

            # Trading methods endpoints
            "trading_methods": f"{self.api_base_url}/api/v1/trading-methods",
            "sub_methods": f"{self.api_base_url}/api/v1/trading-methods/sub-methods",

            # Simulation endpoints
            "simulation": f"{self.api_base_url}/api/v1/simulation",
            "simulation_config": f"{self.api_base_url}/api/v1/simulation/config",
            "simulation_forecast": f"{self.api_base_url}/api/v1/simulation/forecast",

            # Data management endpoints
            "exchanges": f"{self.api_base_url}/api/v1/data/exchanges",
            "instruments": f"{self.api_base_url}/api/v1/data/instruments",
            "market_data": f"{self.api_base_url}/api/v1/data/market",
            "data_import": f"{self.api_base_url}/api/v1/data/import",
            "data_export": f"{self.api_base_url}/api/v1/data/export",
            "data_quality": f"{self.api_base_url}/api/v1/data/quality",

            # Database endpoints
            "database": f"{self.api_base_url}/api/v1/database",
            "database_tables": f"{self.api_base_url}/api/v1/database/tables",
            "database_backups": f"{self.api_base_url}/api/v1/database/backups",
            "database_statistics": f"{self.api_base_url}/api/v1/database/statistics",

            # Settings endpoints
            "settings": f"{self.api_base_url}/api/v1/settings",
            "preferences": f"{self.api_base_url}/api/v1/settings/preferences",

            # System endpoints
            "system_status": f"{self.api_base_url}/api/v1/system/status",
            "system_metrics": f"{self.api_base_url}/api/v1/system/metrics",
            "system_activities": f"{self.api_base_url}/api/v1/system/activities",
        }
    
    def get_chart_settings(self) -> Dict[str, Any]:
        """Get default chart settings"""
        return {
            "chart_type": "candlestick",
            "show_volume": True,
            "show_grid": True,
            "log_scale": False,
            "refresh_time": self.chart_refresh_interval,
            "theme": self.chart_theme,
            "width": self.chart_width,
            "height": self.chart_height,
        }
    
    def get_indicator_colors(self) -> Dict[str, str]:
        """Get default indicator colors"""
        return {
            "sma": "#3498DB",
            "ema": "#9B59B6",
            "rsi": "#E74C3C",
            "macd": "#F39C12",
            "stoch": "#2ECC71",
            "volume": "#95A5A6",
            "support": "#9900ff",
            "resistance": "#ff0099",
        }
    
    def get_moving_average_colors(self) -> list:
        """Get moving average color palette"""
        return [
            "#3498DB",  # Blue
            "#9B59B6",  # Purple
            "#F39C12",  # Orange
            "#1ABC9C",  # Turquoise
            "#8E44AD",  # Violet
            "#E67E22",  # Carrot Orange
            "#16A085",  # Green Sea
            "#D35400",  # Pumpkin
            "#2980B9",  # Belize Hole
            "#8F4BC7",  # Wisteria
        ]
    
    def get_technical_indicator_colors(self) -> list:
        """Get technical indicator color palette"""
        return [
            "#E74C3C",  # Red
            "#F1C40F",  # Yellow
            "#2ECC71",  # Green
            "#E67E22",  # Orange
            "#9B59B6",  # Purple
            "#1ABC9C",  # Turquoise
            "#34495E",  # Dark Blue
            "#E91E63",  # Pink
            "#00BCD4",  # Cyan
            "#FF9800",  # Amber
        ]

    def get_default_trading_params(self) -> Dict[str, Any]:
        """Get default trading parameters"""
        return {
            "risk_per_trade": self.default_risk_per_trade,
            "stop_loss": self.default_stop_loss,
            "take_profit": self.default_take_profit,
            "max_concurrent_positions": self.max_concurrent_positions,
            "commission": self.default_commission,
            "slippage": self.default_slippage,
        }

    def get_simulation_config(self) -> Dict[str, Any]:
        """Get simulation configuration"""
        return {
            "max_history": self.max_simulation_history,
            "timeout": self.simulation_timeout,
            "default_params": self.get_default_trading_params(),
        }

    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "url": self.database_url,
            "backup_frequency": self.backup_frequency,
            "max_backup_files": self.max_backup_files,
            "vacuum_frequency": self.vacuum_frequency,
            "enable_wal_mode": self.enable_wal_mode,
        }

    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration"""
        return {
            "enable_authentication": self.enable_authentication,
            "session_timeout": self.session_timeout,
            "max_login_attempts": self.max_login_attempts,
            "enable_api_key": self.enable_api_key,
            "encrypt_sensitive_data": self.encrypt_sensitive_data,
        }

    def get_notification_config(self) -> Dict[str, Any]:
        """Get notification configuration"""
        return {
            "email": {
                "enabled": self.enable_email_notifications,
                "smtp_server": self.email_smtp_server,
                "smtp_port": self.email_smtp_port,
                "username": self.email_username,
                "password": self.email_password,
            },
            "browser": {
                "enabled": self.enable_browser_notifications,
            },
        }

    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration"""
        return {
            "enable_parallel_processing": self.enable_parallel_processing,
            "max_workers": self.max_workers,
            "cache_ttl": self.data_cache_ttl,
            "max_cache_size": self.max_cache_size,
        }

    def get_file_upload_config(self) -> Dict[str, Any]:
        """Get file upload configuration"""
        return {
            "max_size_mb": self.max_file_size_mb,
            "allowed_types": self.allowed_file_types,
        }

    def get_streamlit_config(self) -> Dict[str, Any]:
        """Get Streamlit-specific configuration"""
        return {
            "page_title": self.page_title,
            "page_icon": self.page_icon,
            "sidebar_width": self.sidebar_width,
            "theme": "light",  # Streamlit theme
        }

    def get_all_settings(self) -> Dict[str, Any]:
        """Get all configuration settings"""
        return {
            "api": {
                "base_url": self.api_base_url,
                "timeout": self.api_timeout,
                "endpoints": self.get_api_endpoints(),
            },
            "chart": self.get_chart_settings(),
            "trading": self.get_default_trading_params(),
            "simulation": self.get_simulation_config(),
            "database": self.get_database_config(),
            "security": self.get_security_config(),
            "notifications": self.get_notification_config(),
            "performance": self.get_performance_config(),
            "file_upload": self.get_file_upload_config(),
            "ui": self.get_streamlit_config(),
            "colors": {
                "indicators": self.get_indicator_colors(),
                "moving_averages": self.get_moving_average_colors(),
                "technical_indicators": self.get_technical_indicator_colors(),
            },
        }

# Global configuration instance
config = AppConfig() 