"""
Application Configuration Module
Handles all configuration settings for the Python frontend
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class AppConfig(BaseSettings):
    """Application configuration settings"""
    
    # API Configuration
    api_base_url: str = Field(
        default="http://localhost:8000",
        env="API_BASE_URL"
    )
    api_timeout: int = Field(
        default=30,
        env="API_TIMEOUT"
    )
    
    # Chart Configuration
    chart_width: int = Field(default=800)
    chart_height: int = Field(default=600)
    chart_theme: str = Field(default="light")
    chart_refresh_interval: int = Field(default=5000)
    
    # Data Configuration
    default_limit: int = Field(default=1000)
    max_data_points: int = Field(default=10000)
    
    # UI Configuration
    sidebar_width: int = Field(default=300)
    page_title: str = Field(default="Trading Simulation")
    page_icon: str = Field(default="📈")
    
    # Development Configuration
    debug: bool = Field(
        default=False,
        env="DEBUG"
    )
    log_level: str = Field(
        default="INFO",
        env="LOG_LEVEL"
    )
    
    # ChartDirector Configuration (when available)
    chartdirector_enabled: bool = Field(default=False)
    chartdirector_license: Optional[str] = Field(
        default=None,
        env="CHARTDIRECTOR_LICENSE"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def get_api_endpoints(self) -> Dict[str, str]:
        """Get API endpoint URLs"""
        return {
            "init_config": f"{self.api_base_url}/api/v1/init-config",
            "chart_data": f"{self.api_base_url}/api/v1/chart/data",
            "chart_config": f"{self.api_base_url}/api/v1/chart/config",
            "indicators": f"{self.api_base_url}/api/v1/indicators/calculate",
            "exchanges": f"{self.api_base_url}/api/v1/database/exchanges",
            "instruments": f"{self.api_base_url}/api/v1/data/instruments",
            "simulation": f"{self.api_base_url}/api/v1/simulation",
        }
    
    def get_chart_settings(self) -> Dict[str, Any]:
        """Get default chart settings"""
        return {
            "chart_type": "candlestick",
            "show_volume": True,
            "show_grid": True,
            "log_scale": False,
            "refresh_time": self.chart_refresh_interval,
            "theme": self.chart_theme,
            "width": self.chart_width,
            "height": self.chart_height,
        }
    
    def get_indicator_colors(self) -> Dict[str, str]:
        """Get default indicator colors"""
        return {
            "sma": "#3498DB",
            "ema": "#9B59B6",
            "rsi": "#E74C3C",
            "macd": "#F39C12",
            "stoch": "#2ECC71",
            "volume": "#95A5A6",
            "support": "#9900ff",
            "resistance": "#ff0099",
        }
    
    def get_moving_average_colors(self) -> list:
        """Get moving average color palette"""
        return [
            "#3498DB",  # Blue
            "#9B59B6",  # Purple
            "#F39C12",  # Orange
            "#1ABC9C",  # Turquoise
            "#8E44AD",  # Violet
            "#E67E22",  # Carrot Orange
            "#16A085",  # Green Sea
            "#D35400",  # Pumpkin
            "#2980B9",  # Belize Hole
            "#8F4BC7",  # Wisteria
        ]
    
    def get_technical_indicator_colors(self) -> list:
        """Get technical indicator color palette"""
        return [
            "#E74C3C",  # Red
            "#F1C40F",  # Yellow
            "#2ECC71",  # Green
            "#E67E22",  # Orange
            "#9B59B6",  # Purple
            "#1ABC9C",  # Turquoise
            "#34495E",  # Dark Blue
            "#E91E63",  # Pink
            "#00BCD4",  # Cyan
            "#FF9800",  # Amber
        ]

# Global configuration instance
config = AppConfig() 