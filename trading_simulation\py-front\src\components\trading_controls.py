"""
Trading Controls Component
Streamlit component for trading controls (instrument selection, timeframe, etc.)
"""

import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
from services.api_service import APIService
from store.app_state import AppState
from utils.constants import TIMEFRAMES

def render_trading_controls(
    api_service: APIService, 
    app_state: AppState,
    show_refresh: bool = True,
    show_status: bool = True,
    container_key: str = "trading_controls"
) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Render trading controls component
    
    Args:
        api_service: API service instance
        app_state: Application state
        show_refresh: Whether to show refresh button
        show_status: Whether to show status indicators
        container_key: Unique key for the container
        
    Returns:
        Tuple of (selected_exchange, selected_symbol, selected_timeframe)
    """
    
    with st.container():
        st.markdown("### 🎯 Trading Controls")
        
        # Create columns for controls
        col1, col2, col3, col4 = st.columns([2, 2, 1, 1])
        
        with col1:
            # Exchange selection
            exchanges = load_exchanges_for_controls(api_service)
            exchange_names = [ex.get('name', 'Unknown') for ex in exchanges]
            
            if exchange_names:
                selected_exchange = st.selectbox(
                    "Exchange",
                    exchange_names,
                    index=0 if not app_state.selected_exchange else 
                          exchange_names.index(app_state.selected_exchange) if app_state.selected_exchange in exchange_names else 0,
                    key=f"{container_key}_exchange"
                )
            else:
                st.selectbox("Exchange", [], disabled=True, placeholder="No exchanges available")
                selected_exchange = None
        
        with col2:
            # Symbol selection
            if selected_exchange:
                instruments = load_instruments_for_controls(api_service, selected_exchange)
                symbol_names = [inst.get('symbol', 'Unknown') for inst in instruments]
                
                if symbol_names:
                    selected_symbol = st.selectbox(
                        "Symbol",
                        symbol_names,
                        index=0 if not app_state.selected_symbol else 
                              symbol_names.index(app_state.selected_symbol) if app_state.selected_symbol in symbol_names else 0,
                        key=f"{container_key}_symbol"
                    )
                    
                    # Update app state with selected instrument
                    selected_instrument = next((inst for inst in instruments if inst.get('symbol') == selected_symbol), None)
                    if selected_instrument:
                        app_state.selected_instrument = selected_instrument
                        app_state.selected_exchange = selected_exchange
                        app_state.selected_symbol = selected_symbol
                else:
                    st.selectbox("Symbol", [], disabled=True, placeholder="No symbols available")
                    selected_symbol = None
            else:
                st.selectbox("Symbol", [], disabled=True, placeholder="Select exchange first")
                selected_symbol = None
        
        with col3:
            # Timeframe selection
            timeframe_options = [tf["value"] for tf in TIMEFRAMES]
            timeframe_labels = [tf["label"] for tf in TIMEFRAMES]
            
            current_index = 0
            if app_state.selected_timeframe in timeframe_options:
                current_index = timeframe_options.index(app_state.selected_timeframe)
            
            selected_timeframe = st.selectbox(
                "Timeframe",
                timeframe_options,
                format_func=lambda x: next(tf["label"] for tf in TIMEFRAMES if tf["value"] == x),
                index=current_index,
                key=f"{container_key}_timeframe"
            )
            
            app_state.selected_timeframe = selected_timeframe
        
        with col4:
            # Refresh button
            if show_refresh:
                if st.button("🔄 Refresh", key=f"{container_key}_refresh"):
                    refresh_trading_data(api_service, app_state)
        
        # Status indicators
        if show_status and app_state.selected_instrument:
            st.markdown("---")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                connection_status = check_connection_status(api_service)
                status_color = "🟢" if connection_status else "🔴"
                st.markdown(f"**Connection:** {status_color}")
            
            with col2:
                data_status = check_data_status(api_service, app_state)
                status_color = "🟢" if data_status else "🔴"
                st.markdown(f"**Data:** {status_color}")
            
            with col3:
                last_update = get_last_update_time(api_service, app_state)
                st.markdown(f"**Last Update:** {last_update}")
        
        return selected_exchange, selected_symbol, selected_timeframe

def render_chart_settings(
    app_state: AppState,
    container_key: str = "chart_settings"
) -> Dict[str, Any]:
    """
    Render chart settings component
    
    Args:
        app_state: Application state
        container_key: Unique key for the container
        
    Returns:
        Dictionary of chart settings
    """
    
    with st.expander("📊 Chart Settings"):
        col1, col2 = st.columns(2)
        
        with col1:
            chart_type = st.selectbox(
                "Chart Type",
                ["candlestick", "line", "area"],
                index=0,
                key=f"{container_key}_chart_type"
            )
            
            show_volume = st.checkbox(
                "Show Volume",
                value=True,
                key=f"{container_key}_volume"
            )
        
        with col2:
            show_grid = st.checkbox(
                "Show Grid",
                value=True,
                key=f"{container_key}_grid"
            )
            
            log_scale = st.checkbox(
                "Log Scale",
                value=False,
                key=f"{container_key}_log_scale"
            )
        
        settings = {
            "chart_type": chart_type,
            "show_volume": show_volume,
            "show_grid": show_grid,
            "log_scale": log_scale,
        }
        
        # Update app state
        app_state.update_chart_settings(settings)
        
        return settings

def render_trading_status(
    app_state: AppState,
    container_key: str = "trading_status"
) -> None:
    """
    Render trading status component
    
    Args:
        app_state: Application state
        container_key: Unique key for the container
    """
    
    if not app_state.selected_instrument:
        st.info("No instrument selected")
        return
    
    with st.container():
        st.markdown("### 📈 Trading Status")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Current Price",
                "$45,250.00",
                delta="$125.50 (0.28%)"
            )
        
        with col2:
            st.metric(
                "24h Volume",
                "1.2M",
                delta="15.2%"
            )
        
        with col3:
            st.metric(
                "Active Methods",
                "3",
                delta="1"
            )
        
        with col4:
            st.metric(
                "Success Rate",
                "67.5%",
                delta="2.1%"
            )

# Helper Functions

def load_exchanges_for_controls(api_service: APIService) -> List[Dict[str, Any]]:
    """Load exchanges for trading controls"""
    try:
        # This would be an actual API call
        return [
            {'id': 1, 'name': 'Binance'},
            {'id': 2, 'name': 'Coinbase'},
            {'id': 3, 'name': 'Kraken'}
        ]
    except Exception:
        return []

def load_instruments_for_controls(api_service: APIService, exchange: str) -> List[Dict[str, Any]]:
    """Load instruments for trading controls"""
    try:
        # This would be an actual API call
        instruments_map = {
            'Binance': [
                {'id': 1, 'symbol': 'BTCUSDT', 'exchange': 'Binance'},
                {'id': 2, 'symbol': 'ETHUSDT', 'exchange': 'Binance'},
                {'id': 3, 'symbol': 'ADAUSDT', 'exchange': 'Binance'}
            ],
            'Coinbase': [
                {'id': 4, 'symbol': 'BTC-USD', 'exchange': 'Coinbase'},
                {'id': 5, 'symbol': 'ETH-USD', 'exchange': 'Coinbase'}
            ],
            'Kraken': [
                {'id': 6, 'symbol': 'XBTUSD', 'exchange': 'Kraken'},
                {'id': 7, 'symbol': 'ETHUSD', 'exchange': 'Kraken'}
            ]
        }
        return instruments_map.get(exchange, [])
    except Exception:
        return []

def refresh_trading_data(api_service: APIService, app_state: AppState) -> None:
    """Refresh trading data"""
    try:
        st.success("✅ Trading data refreshed!")
    except Exception as e:
        st.error(f"Failed to refresh data: {str(e)}")

def check_connection_status(api_service: APIService) -> bool:
    """Check connection status"""
    try:
        # This would be an actual API call
        return True
    except Exception:
        return False

def check_data_status(api_service: APIService, app_state: AppState) -> bool:
    """Check data status"""
    try:
        # This would be an actual API call
        return True
    except Exception:
        return False

def get_last_update_time(api_service: APIService, app_state: AppState) -> str:
    """Get last update time"""
    try:
        # This would be an actual API call
        return "2 min ago"
    except Exception:
        return "Unknown"
