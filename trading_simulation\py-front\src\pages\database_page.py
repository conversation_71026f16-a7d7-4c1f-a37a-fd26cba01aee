"""
Database Management Page
Database operations, table management, and data generation
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
from services.api_service import APIService
from store.app_state import AppState

def database_page():
    """Main database management page"""
    
    st.title("🗄️ Database Management")
    st.markdown("Manage database tables, operations, and data generation.")
    
    # Get application state
    app_state: AppState = st.session_state.app_state
    api_service: APIService = st.session_state.api_service
    
    # Create tabs for different sections
    tab1, tab2, tab3, tab4 = st.tabs([
        "📋 Tables", 
        "🔧 Operations", 
        "📊 Data Generation", 
        "📈 Statistics"
    ])
    
    with tab1:
        tables_tab(api_service, app_state)
    
    with tab2:
        operations_tab(api_service, app_state)
    
    with tab3:
        data_generation_tab(api_service, app_state)
    
    with tab4:
        statistics_tab(api_service, app_state)

def tables_tab(api_service: APIService, app_state: AppState):
    """Database tables management tab"""
    
    st.subheader("Database Tables")
    
    # Load tables if not already loaded
    if 'database_tables' not in st.session_state:
        load_database_tables(api_service)
    
    tables = st.session_state.get('database_tables', [])
    
    if not tables:
        st.warning("No database tables found.")
        if st.button("🔄 Refresh Tables"):
            load_database_tables(api_service)
        return
    
    # Table selection
    selected_table = st.selectbox(
        "Select Table",
        [table.get('name', 'Unknown') for table in tables],
        key="selected_table"
    )
    
    if selected_table:
        table_info = next((table for table in tables if table.get('name') == selected_table), None)
        
        if table_info:
            # Table information
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Records", table_info.get('record_count', 0))
            
            with col2:
                st.metric("Columns", table_info.get('column_count', 0))
            
            with col3:
                st.metric("Size", f"{table_info.get('size_mb', 0):.2f} MB")
            
            with col4:
                last_updated = table_info.get('last_updated', 'Unknown')
                st.metric("Last Updated", last_updated)
            
            # Table data preview
            st.markdown("### Table Data Preview")
            
            # Pagination controls
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col1:
                page_size = st.selectbox("Rows per page", [10, 25, 50, 100], index=1)
            
            with col2:
                current_page = st.number_input(
                    "Page", 
                    min_value=1, 
                    max_value=max(1, (table_info.get('record_count', 0) // page_size) + 1),
                    value=1
                )
            
            with col3:
                if st.button("🔄 Refresh Data"):
                    load_table_data(selected_table, current_page, page_size, api_service)
            
            # Load and display table data
            if st.button("📊 Load Table Data") or f'table_data_{selected_table}' in st.session_state:
                load_table_data(selected_table, current_page, page_size, api_service)
                
                table_data = st.session_state.get(f'table_data_{selected_table}')
                if table_data is not None:
                    st.dataframe(table_data, use_container_width=True)
                    
                    # Export options
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        if st.button("📥 Export CSV"):
                            export_table_data(selected_table, 'csv', api_service)
                    
                    with col2:
                        if st.button("📥 Export JSON"):
                            export_table_data(selected_table, 'json', api_service)

def operations_tab(api_service: APIService, app_state: AppState):
    """Database operations tab"""
    
    st.subheader("Database Operations")
    
    # Database status
    st.markdown("### 📊 Database Status")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Connection", "✅ Connected")
    
    with col2:
        st.metric("Version", "SQLite 3.39")
    
    with col3:
        st.metric("Size", "125.6 MB")
    
    with col4:
        st.metric("Tables", "12")
    
    # Backup operations
    st.markdown("### 💾 Backup Operations")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("📦 Create Backup", use_container_width=True):
            create_database_backup(api_service)
    
    with col2:
        if st.button("📂 List Backups", use_container_width=True):
            list_database_backups(api_service)
    
    # Display backups if available
    if 'database_backups' in st.session_state:
        backups = st.session_state.database_backups
        
        if backups:
            st.markdown("**Available Backups:**")
            
            for backup in backups:
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.text(backup.get('filename', 'Unknown'))
                
                with col2:
                    st.text(backup.get('created', 'Unknown'))
                
                with col3:
                    if st.button("🔄 Restore", key=f"restore_{backup.get('id')}"):
                        restore_database_backup(backup.get('filename'), api_service)
    
    # Maintenance operations
    st.markdown("### 🔧 Maintenance")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🧹 Vacuum Database", use_container_width=True):
            vacuum_database(api_service)
    
    with col2:
        if st.button("📊 Analyze Database", use_container_width=True):
            analyze_database(api_service)
    
    with col3:
        if st.button("🔍 Check Integrity", use_container_width=True):
            check_database_integrity(api_service)

def data_generation_tab(api_service: APIService, app_state: AppState):
    """Data generation tab"""
    
    st.subheader("Data Generation")
    
    # Table selection for data generation
    if 'database_tables' not in st.session_state:
        load_database_tables(api_service)
    
    tables = st.session_state.get('database_tables', [])
    table_names = [table.get('name', 'Unknown') for table in tables]
    
    selected_table_for_generation = st.selectbox(
        "Select Table for Data Generation",
        table_names,
        key="generation_table"
    )
    
    if selected_table_for_generation:
        # Generation parameters
        col1, col2 = st.columns(2)
        
        with col1:
            generation_amount = st.number_input(
                "Number of Records to Generate",
                min_value=1,
                max_value=10000,
                value=100
            )
        
        with col2:
            generation_method = st.selectbox(
                "Generation Method",
                ["Random", "Pattern-based", "Historical-based"],
                index=0
            )
        
        # Special parameters for market_data table
        if selected_table_for_generation == 'market_data':
            st.markdown("### Market Data Parameters")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                # Load exchanges for selection
                exchanges = load_exchanges_for_generation(api_service)
                selected_exchange = st.selectbox(
                    "Exchange",
                    [ex.get('name', 'Unknown') for ex in exchanges]
                )
            
            with col2:
                selected_pair = st.selectbox(
                    "Trading Pair",
                    ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT"]
                )
            
            with col3:
                selected_timeframe = st.selectbox(
                    "Timeframe",
                    ["1m", "5m", "15m", "1h", "4h", "1d"]
                )
        
        # Generate data
        if st.button("🎲 Generate Data", use_container_width=True):
            if selected_table_for_generation == 'market_data':
                generate_market_data(
                    generation_amount, selected_exchange, selected_pair, 
                    selected_timeframe, generation_method, api_service
                )
            else:
                generate_table_data(
                    selected_table_for_generation, generation_amount, 
                    generation_method, api_service
                )

def statistics_tab(api_service: APIService, app_state: AppState):
    """Database statistics tab"""
    
    st.subheader("Database Statistics")
    
    # Overall statistics
    st.markdown("### 📊 Overall Statistics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Records", "1,250,000")
    
    with col2:
        st.metric("Total Tables", "12")
    
    with col3:
        st.metric("Database Size", "125.6 MB")
    
    with col4:
        st.metric("Index Count", "28")
    
    # Table statistics
    st.markdown("### 📋 Table Statistics")
    
    if st.button("📊 Load Table Statistics"):
        load_table_statistics(api_service)
    
    if 'table_statistics' in st.session_state:
        stats_df = st.session_state.table_statistics
        st.dataframe(stats_df, use_container_width=True)
    
    # Query performance
    st.markdown("### ⚡ Query Performance")
    
    if st.button("🔍 Analyze Query Performance"):
        analyze_query_performance(api_service)
    
    if 'query_performance' in st.session_state:
        perf_data = st.session_state.query_performance
        
        for query_info in perf_data:
            with st.expander(f"Query: {query_info.get('query_type', 'Unknown')}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("Avg Time", f"{query_info.get('avg_time', 0):.2f}ms")
                
                with col2:
                    st.metric("Executions", query_info.get('executions', 0))
                
                with col3:
                    st.metric("Cache Hit Rate", f"{query_info.get('cache_hit_rate', 0):.1f}%")

# Helper Functions

def load_database_tables(api_service: APIService):
    """Load database tables information"""
    
    try:
        # Placeholder data - would be actual API call
        tables = [
            {
                'name': 'market_data',
                'record_count': 500000,
                'column_count': 7,
                'size_mb': 85.2,
                'last_updated': '2024-01-15 10:30:00'
            },
            {
                'name': 'exchanges',
                'record_count': 5,
                'column_count': 4,
                'size_mb': 0.1,
                'last_updated': '2024-01-10 15:20:00'
            },
            {
                'name': 'instruments',
                'record_count': 150,
                'column_count': 6,
                'size_mb': 0.5,
                'last_updated': '2024-01-12 09:15:00'
            },
            {
                'name': 'trading_methods',
                'record_count': 12,
                'column_count': 8,
                'size_mb': 0.2,
                'last_updated': '2024-01-14 14:45:00'
            }
        ]
        
        st.session_state.database_tables = tables
        st.success("✅ Database tables loaded successfully!")
    
    except Exception as e:
        st.error(f"Failed to load database tables: {str(e)}")

def load_table_data(table_name: str, page: int, page_size: int, api_service: APIService):
    """Load data for a specific table"""
    
    try:
        # Generate sample data based on table name
        if table_name == 'market_data':
            import numpy as np
            from datetime import datetime, timedelta
            
            dates = pd.date_range(start=datetime.now() - timedelta(days=page_size), end=datetime.now(), freq='1H')
            
            data = {
                'id': range(1, len(dates) + 1),
                'timestamp': dates,
                'open': np.random.uniform(40000, 45000, len(dates)),
                'high': np.random.uniform(45000, 50000, len(dates)),
                'low': np.random.uniform(35000, 40000, len(dates)),
                'close': np.random.uniform(40000, 45000, len(dates)),
                'volume': np.random.uniform(100, 1000, len(dates))
            }
        
        elif table_name == 'exchanges':
            data = {
                'id': [1, 2, 3],
                'name': ['Binance', 'Coinbase', 'Kraken'],
                'type': ['Crypto', 'Crypto', 'Crypto'],
                'is_active': [True, True, False]
            }
        
        else:
            # Generic sample data
            data = {
                'id': range(1, page_size + 1),
                'name': [f'Item {i}' for i in range(1, page_size + 1)],
                'value': np.random.uniform(0, 100, page_size)
            }
        
        df = pd.DataFrame(data)
        st.session_state[f'table_data_{table_name}'] = df
        
    except Exception as e:
        st.error(f"Failed to load table data: {str(e)}")

def export_table_data(table_name: str, format: str, api_service: APIService):
    """Export table data"""
    
    try:
        st.success(f"✅ Table '{table_name}' exported in {format.upper()} format")
    except Exception as e:
        st.error(f"Failed to export table data: {str(e)}")

def create_database_backup(api_service: APIService):
    """Create database backup"""
    
    try:
        st.success("✅ Database backup created successfully!")
    except Exception as e:
        st.error(f"Failed to create backup: {str(e)}")

def list_database_backups(api_service: APIService):
    """List available database backups"""
    
    try:
        backups = [
            {
                'id': 1,
                'filename': 'backup_2024_01_15_10_30.db',
                'created': '2024-01-15 10:30:00',
                'size': '125.6 MB'
            },
            {
                'id': 2,
                'filename': 'backup_2024_01_14_18_45.db',
                'created': '2024-01-14 18:45:00',
                'size': '124.2 MB'
            }
        ]
        
        st.session_state.database_backups = backups
        st.success("✅ Backup list loaded successfully!")
    
    except Exception as e:
        st.error(f"Failed to list backups: {str(e)}")

def restore_database_backup(filename: str, api_service: APIService):
    """Restore database from backup"""
    
    try:
        st.success(f"✅ Database restored from {filename}")
    except Exception as e:
        st.error(f"Failed to restore backup: {str(e)}")

def vacuum_database(api_service: APIService):
    """Vacuum database"""
    st.success("✅ Database vacuum completed!")

def analyze_database(api_service: APIService):
    """Analyze database"""
    st.success("✅ Database analysis completed!")

def check_database_integrity(api_service: APIService):
    """Check database integrity"""
    st.success("✅ Database integrity check passed!")

def load_exchanges_for_generation(api_service: APIService) -> List[Dict]:
    """Load exchanges for data generation"""
    
    return [
        {'id': 1, 'name': 'Binance'},
        {'id': 2, 'name': 'Coinbase'},
        {'id': 3, 'name': 'Kraken'}
    ]

def generate_market_data(amount: int, exchange: str, pair: str, timeframe: str, method: str, api_service: APIService):
    """Generate market data"""
    
    try:
        st.success(f"✅ Generated {amount} market data records for {exchange}:{pair} ({timeframe})")
    except Exception as e:
        st.error(f"Failed to generate market data: {str(e)}")

def generate_table_data(table_name: str, amount: int, method: str, api_service: APIService):
    """Generate data for a table"""
    
    try:
        st.success(f"✅ Generated {amount} records for table '{table_name}'")
    except Exception as e:
        st.error(f"Failed to generate table data: {str(e)}")

def load_table_statistics(api_service: APIService):
    """Load table statistics"""
    
    try:
        stats_data = {
            'Table': ['market_data', 'exchanges', 'instruments', 'trading_methods'],
            'Records': [500000, 5, 150, 12],
            'Size (MB)': [85.2, 0.1, 0.5, 0.2],
            'Indexes': [3, 1, 2, 2],
            'Last Updated': ['2024-01-15', '2024-01-10', '2024-01-12', '2024-01-14']
        }
        
        df = pd.DataFrame(stats_data)
        st.session_state.table_statistics = df
        
    except Exception as e:
        st.error(f"Failed to load table statistics: {str(e)}")

def analyze_query_performance(api_service: APIService):
    """Analyze query performance"""
    
    try:
        performance_data = [
            {
                'query_type': 'SELECT market_data',
                'avg_time': 15.2,
                'executions': 1250,
                'cache_hit_rate': 85.5
            },
            {
                'query_type': 'INSERT market_data',
                'avg_time': 2.8,
                'executions': 500,
                'cache_hit_rate': 0.0
            }
        ]
        
        st.session_state.query_performance = performance_data
        
    except Exception as e:
        st.error(f"Failed to analyze query performance: {str(e)}")
