"""
Application Constants
Constants used throughout the application
"""

from typing import Dict, List, Any

# API Configuration
DEFAULT_API_TIMEOUT = 30
DEFAULT_API_BASE_URL = "http://localhost:8000"

# Chart Configuration
DEFAULT_CHART_WIDTH = 800
DEFAULT_CHART_HEIGHT = 600
DEFAULT_CHART_THEME = "light"
DEFAULT_REFRESH_INTERVAL = 5000

# Timeframes
TIMEFRAMES = [
    {"value": "1m", "label": "1 Minute"},
    {"value": "5m", "label": "5 Minutes"},
    {"value": "15m", "label": "15 Minutes"},
    {"value": "1h", "label": "1 Hour"},
    {"value": "4h", "label": "4 Hours"},
    {"value": "1d", "label": "1 Day"},
    {"value": "1w", "label": "1 Week"},
]

# Chart Types
CHART_TYPES = [
    {"value": "candlestick", "label": "Candlestick"},
    {"value": "line", "label": "Line"},
    {"value": "area", "label": "Area"},
    {"value": "histogram", "label": "Histogram"},
]

# Band Types
BAND_TYPES = [
    {"value": "none", "label": "None"},
    {"value": "bb", "label": "Bollinger Bands"},
    {"value": "dc", "label": "Donchian Channels"},
    {"value": "envelop", "label": "Envelopes"},
]

# Moving Average Types
MOVING_AVERAGE_TYPES = [
    {"value": "none", "label": "None"},
    {"value": "sma", "label": "Simple Moving Average"},
    {"value": "ema", "label": "Exponential Moving Average"},
    {"value": "tma", "label": "Triangular Moving Average"},
    {"value": "wma", "label": "Weighted Moving Average"},
]

# Technical Indicators
TECHNICAL_INDICATORS = [
    {"value": "none", "label": "None"},
    {"value": "rsi", "label": "RSI"},
    {"value": "macd", "label": "MACD"},
    {"value": "stoch", "label": "Stochastic"},
    {"value": "bb", "label": "Bollinger Bands"},
    {"value": "atr", "label": "ATR"},
    {"value": "adx", "label": "ADX"},
    {"value": "cci", "label": "CCI"},
    {"value": "williams", "label": "Williams %R"},
    {"value": "obv", "label": "OBV"},
    {"value": "volume", "label": "Volume"},
    {"value": "mfi", "label": "MFI"},
    {"value": "trix", "label": "TRIX"},
    {"value": "roc", "label": "ROC"},
    {"value": "aroon", "label": "Aroon"},
    {"value": "momentum", "label": "Momentum"},
    {"value": "ppo", "label": "PPO"},
    {"value": "stochrsi", "label": "Stochastic RSI"},
]

# Simulation Types
SIMULATION_TYPES = [
    {"value": "backtest", "label": "Backtest"},
    {"value": "forward_test", "label": "Forward Test"},
    {"value": "monte_carlo", "label": "Monte Carlo"},
    {"value": "parameter_optimization", "label": "Parameter Optimization"},
]

# Exchange Types
EXCHANGE_TYPES = [
    {"value": "crypto", "label": "Cryptocurrency"},
    {"value": "stock", "label": "Stock"},
    {"value": "forex", "label": "Forex"},
    {"value": "commodity", "label": "Commodity"},
]

# Data Export Formats
EXPORT_FORMATS = [
    {"value": "csv", "label": "CSV"},
    {"value": "json", "label": "JSON"},
    {"value": "excel", "label": "Excel"},
]

# Theme Options
THEME_OPTIONS = [
    {"value": "light", "label": "Light"},
    {"value": "dark", "label": "Dark"},
    {"value": "auto", "label": "Auto"},
]

# Color Schemes
COLOR_SCHEMES = [
    {"value": "default", "label": "Default"},
    {"value": "blue", "label": "Blue"},
    {"value": "green", "label": "Green"},
    {"value": "purple", "label": "Purple"},
    {"value": "orange", "label": "Orange"},
]

# Font Sizes
FONT_SIZES = [
    {"value": "small", "label": "Small"},
    {"value": "medium", "label": "Medium"},
    {"value": "large", "label": "Large"},
]

# Grid Styles
GRID_STYLES = [
    {"value": "solid", "label": "Solid"},
    {"value": "dashed", "label": "Dashed"},
    {"value": "dotted", "label": "Dotted"},
    {"value": "none", "label": "None"},
]

# Backup Frequencies
BACKUP_FREQUENCIES = [
    {"value": "never", "label": "Never"},
    {"value": "daily", "label": "Daily"},
    {"value": "weekly", "label": "Weekly"},
    {"value": "monthly", "label": "Monthly"},
]

# Vacuum Frequencies
VACUUM_FREQUENCIES = [
    {"value": "never", "label": "Never"},
    {"value": "weekly", "label": "Weekly"},
    {"value": "monthly", "label": "Monthly"},
]

# Data Generation Methods
DATA_GENERATION_METHODS = [
    {"value": "random", "label": "Random"},
    {"value": "pattern_based", "label": "Pattern-based"},
    {"value": "historical_based", "label": "Historical-based"},
]

# Activity Types
ACTIVITY_TYPES = [
    {"value": "simulation", "label": "Simulation"},
    {"value": "data", "label": "Data"},
    {"value": "method", "label": "Method"},
    {"value": "system", "label": "System"},
]

# Activity Status
ACTIVITY_STATUS = [
    {"value": "success", "label": "Success", "icon": "✅"},
    {"value": "warning", "label": "Warning", "icon": "⚠️"},
    {"value": "error", "label": "Error", "icon": "❌"},
    {"value": "info", "label": "Info", "icon": "ℹ️"},
]

# Default Colors for Charts
DEFAULT_COLORS = [
    "#3498DB",  # Blue
    "#E74C3C",  # Red
    "#2ECC71",  # Green
    "#F39C12",  # Orange
    "#9B59B6",  # Purple
    "#1ABC9C",  # Turquoise
    "#E67E22",  # Carrot
    "#95A5A6",  # Silver
    "#34495E",  # Wet Asphalt
    "#E91E63",  # Pink
]

# Line Styles
LINE_STYLES = [
    {"value": "solid", "label": "Solid"},
    {"value": "dashed", "label": "Dashed"},
    {"value": "dotted", "label": "Dotted"},
    {"value": "dashdot", "label": "Dash-Dot"},
]

# Line Widths
LINE_WIDTHS = [1, 2, 3, 4, 5]

# Default Indicator Parameters
DEFAULT_INDICATOR_PARAMS = {
    "rsi": {"period": 14},
    "macd": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
    "stoch": {"k_period": 14, "d_period": 3},
    "bb": {"period": 20, "deviation": 2},
    "atr": {"period": 14},
    "adx": {"period": 14},
    "cci": {"period": 20},
    "williams": {"period": 14},
    "obv": {},
    "volume": {},
    "mfi": {"period": 14},
    "trix": {"period": 14},
    "roc": {"period": 10},
    "aroon": {"period": 14},
    "momentum": {"period": 10},
    "ppo": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
    "stochrsi": {"period": 14, "k_period": 3, "d_period": 3},
}

# Default Trading Parameters
DEFAULT_TRADING_PARAMS = {
    "risk_per_trade": 2.0,
    "stop_loss": 5.0,
    "take_profit": 10.0,
    "max_concurrent_positions": 5,
    "commission": 5.0,
    "slippage": 0.1,
}

# Database Table Names
DATABASE_TABLES = [
    "market_data",
    "exchanges",
    "instruments",
    "trading_methods",
    "simulations",
    "configurations",
    "users",
    "activities",
]

# Page Sizes for Pagination
PAGE_SIZES = [10, 25, 50, 100, 250, 500]

# Status Colors
STATUS_COLORS = {
    "success": "#2ECC71",
    "warning": "#F39C12",
    "error": "#E74C3C",
    "info": "#3498DB",
    "active": "#2ECC71",
    "inactive": "#95A5A6",
}

# Navigation Icons (using emoji for Streamlit)
NAVIGATION_ICONS = {
    "dashboard": "📈",
    "methods": "🎯",
    "simulation": "🚀",
    "data": "📊",
    "settings": "⚙️",
    "database": "🗄️",
}

# File Upload Limits
MAX_FILE_SIZE_MB = 100
ALLOWED_FILE_TYPES = ["csv", "json", "xlsx"]

# Cache Settings
DEFAULT_CACHE_TTL = 300  # 5 minutes
MAX_CACHE_SIZE = 1000

# Error Messages
ERROR_MESSAGES = {
    "no_instrument_selected": "No instrument selected. Please select an instrument first.",
    "api_connection_failed": "Failed to connect to the API server.",
    "invalid_file_format": "Invalid file format. Please upload a valid file.",
    "file_too_large": f"File too large. Maximum size is {MAX_FILE_SIZE_MB}MB.",
    "simulation_failed": "Simulation failed to complete.",
    "database_error": "Database operation failed.",
    "invalid_parameters": "Invalid parameters provided.",
}

# Success Messages
SUCCESS_MESSAGES = {
    "data_imported": "Data imported successfully!",
    "simulation_completed": "Simulation completed successfully!",
    "settings_saved": "Settings saved successfully!",
    "backup_created": "Database backup created successfully!",
    "configuration_updated": "Configuration updated successfully!",
}

# Default Pagination
DEFAULT_PAGE_SIZE = 50
MAX_PAGE_SIZE = 1000
