# Trading Simulation Python Frontend

A Streamlit-based frontend for the Trading Simulation System, migrated from the original React frontend with full feature parity (excluding charts which will use ChartDirector).

## Features

### 📈 Dashboard
- System metrics display (active instruments, total methods, data points, success rate)
- System status monitoring (database, API, data feed, simulation)
- Recent activity feed with different activity types
- Quick action buttons for navigation
- Instrument selection (moved from Chart page)

### 🎯 Trading Methods
- Trading methods management and configuration
- Method filtering and search functionality
- Method activation/deactivation controls
- Weight configuration with sliders
- Sub-method management
- Method testing and comparison
- Parameter configuration dialogs
- Performance metrics display

### 🚀 Simulation
- Simulation setup and configuration
- Real-time simulation progress tracking
- Results display and analysis
- Performance analysis with charts
- Forecast generation and display
- Simulation history management

### 📊 Data Management
- Exchange configuration and management
- Market data import/export
- Real-time data feeds
- Data validation and quality checks
- Database integration

### ⚙️ Settings
- Application settings
- User preferences
- Theme and display options
- Database configuration
- System preferences
- Security settings
- Notification configuration

### 🗄️ Database
- Database table management
- Table data viewing with pagination
- Data generation functionality
- Table schema inspection
- Database operations (backup, restore)
- Performance monitoring

### 📊 Chart View (Under Construction)
- Shows construction notice
- Will be replaced with ChartDirector implementation
- Provides navigation to other sections

## Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd trading_simulation/py-front
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables** (optional):
   Create a `.env` file in the py-front directory:
   ```env
   API_BASE_URL=http://localhost:8000
   API_TIMEOUT=30
   DEBUG=false
   ```

## Running the Application

### Method 1: Using the run script
```bash
python run.py
```

### Method 2: Direct Streamlit command
```bash
streamlit run app.py --server.port 8501
```

### Method 3: Using Python module
```bash
python -m streamlit run app.py --server.port 8501
```

The application will be available at: http://localhost:8501

## Configuration

The application uses `src/config/app_config.py` for configuration management. Key settings include:

- **API Configuration**: Base URL, timeout, endpoints
- **Trading Parameters**: Default risk, stop loss, take profit
- **Database Settings**: Connection, backup, maintenance
- **Security Settings**: Authentication, API keys, encryption
- **Performance Settings**: Parallel processing, caching

## Architecture

### Directory Structure
```
py-front/
├── app.py                 # Main application entry point
├── run.py                 # Startup script
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── src/
    ├── pages/            # Streamlit pages
    │   ├── main_dashboard.py
    │   ├── chart_page.py
    │   ├── methods_page.py
    │   ├── simulation_page.py
    │   ├── data_page.py
    │   ├── settings_page.py
    │   └── database_page.py
    ├── components/       # Reusable components
    │   ├── trading_controls.py
    │   ├── parameter_form.py
    │   └── performance_metrics.py
    ├── services/         # API and external services
    │   └── api_service.py
    ├── store/           # State management
    │   └── app_state.py
    ├── utils/           # Utility functions
    │   ├── constants.py
    │   ├── formatters.py
    │   └── instrument_utils.py
    └── config/          # Configuration
        └── app_config.py
```

### State Management
- Uses Streamlit's session state for global application state
- `AppState` class manages all application data
- Persistent across page navigation
- Includes trading methods, simulation results, database state, etc.

### API Integration
- `APIService` class handles all backend communication
- Supports all endpoints from the original React frontend
- Includes error handling and timeout management
- Compatible with existing FastAPI backend

## Migration from React Frontend

This Streamlit frontend provides complete feature parity with the original React frontend, with the following key differences:

### ✅ Migrated Features
- All pages (Dashboard, Methods, Simulation, Data, Settings, Database)
- Complete navigation system
- State management (Redux → Streamlit session state)
- API service integration
- All utility functions and constants
- Configuration system
- Error handling

### 🚧 Chart Functionality
- Chart page shows construction notice
- Will be replaced with ChartDirector implementation
- Instrument selection moved to Dashboard
- All non-chart functionality fully migrated

### 🔄 Technology Changes
- React/TypeScript → Streamlit/Python
- Material-UI → Streamlit native components
- Redux → Streamlit session state
- Axios → Python requests/httpx
- React Router → Streamlit page navigation

## Backend Compatibility

The py-front application is fully compatible with the existing FastAPI backend:

- Uses the same API endpoints
- Maintains the same data structures
- Supports all existing functionality
- No backend changes required

## Development

### Adding New Pages
1. Create a new file in `src/pages/`
2. Import and add to `app.py` navigation
3. Follow existing page patterns

### Adding New Components
1. Create a new file in `src/components/`
2. Follow the component pattern with proper typing
3. Import and use in pages as needed

### Modifying State
1. Update `AppState` class in `src/store/app_state.py`
2. Add methods for state management
3. Use in pages via `st.session_state.app_state`

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed via `pip install -r requirements.txt`

2. **API Connection Issues**: Check that the backend is running on the configured URL (default: http://localhost:8000)

3. **Page Navigation Issues**: Ensure all page files exist in `src/pages/` and are properly imported in `app.py`

4. **State Issues**: Clear browser cache or restart the Streamlit server

### Debug Mode
Set `DEBUG=true` in your `.env` file or environment variables to enable debug logging.

## Future Enhancements

- ChartDirector integration for professional charting
- Enhanced real-time updates
- Advanced data visualization
- Mobile responsiveness improvements
- Performance optimizations

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the backend API documentation
3. Check Streamlit documentation for UI-related issues
