"""
Trading Methods Page
Manages trading methods configuration, testing, and comparison
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
import asyncio
from services.api_service import APIService
from store.app_state import AppState

def methods_page():
    """Main trading methods page"""
    
    st.title("🎯 Trading Methods")
    st.markdown("Configure, test, and compare trading methods for optimal performance.")
    
    # Initialize services
    if 'api_service' not in st.session_state:
        st.session_state.api_service = APIService()
    
    if 'app_state' not in st.session_state:
        st.session_state.app_state = AppState()
    
    api_service = st.session_state.api_service
    app_state = st.session_state.app_state
    
    # Check if instrument is selected
    if not app_state.selected_instrument:
        st.warning("⚠️ No instrument selected. Please select an instrument in the Dashboard first.")
        if st.button("Go to Dashboard"):
            st.switch_page("pages/main_dashboard.py")
        return
    
    # Create tabs for different sections
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 Methods Overview", 
        "⚙️ Configuration", 
        "🧪 Testing", 
        "📈 Comparison"
    ])
    
    with tab1:
        methods_overview_tab(api_service, app_state)
    
    with tab2:
        methods_configuration_tab(api_service, app_state)
    
    with tab3:
        methods_testing_tab(api_service, app_state)
    
    with tab4:
        methods_comparison_tab(api_service, app_state)

def methods_overview_tab(api_service: APIService, app_state: AppState):
    """Methods overview and management tab"""
    
    st.subheader("Trading Methods Overview")
    
    # Search and filter controls
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        search_term = st.text_input("🔍 Search methods", placeholder="Search by name or description...")
    
    with col2:
        filter_active = st.selectbox("Filter by status", ["All", "Active", "Inactive"])
    
    with col3:
        if st.button("🔄 Refresh Methods", use_container_width=True):
            load_trading_methods(api_service, app_state)
    
    # Load methods if not already loaded
    if 'trading_methods' not in st.session_state:
        load_trading_methods(api_service, app_state)
    
    methods = st.session_state.get('trading_methods', [])
    
    if not methods:
        st.info("No trading methods found. Please check your instrument selection.")
        return
    
    # Filter methods based on search and filter criteria
    filtered_methods = filter_methods(methods, search_term, filter_active)
    
    # Display methods in cards
    for method in filtered_methods:
        display_method_card(method, api_service, app_state)

def display_method_card(method: Dict[str, Any], api_service: APIService, app_state: AppState):
    """Display a single method card"""
    
    with st.container():
        col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
        
        with col1:
            # Method name and description
            method_name = method.get('name') or method.get('method_name', 'Unknown Method')
            st.markdown(f"**{method_name}**")
            st.caption(method.get('description', 'No description available'))
        
        with col2:
            # Status indicator
            is_active = method.get('is_active', True)
            status_color = "🟢" if is_active else "🔴"
            st.markdown(f"{status_color} {'Active' if is_active else 'Inactive'}")
        
        with col3:
            # Performance metrics
            success_rate = method.get('success_rate', 0)
            st.metric("Success Rate", f"{success_rate:.1f}%")
        
        with col4:
            # Action buttons
            if st.button("⚙️ Configure", key=f"config_{method.get('id')}"):
                st.session_state.selected_method = method
                st.rerun()
        
        # Weight slider
        weight = method.get('weight', 1.0)
        new_weight = st.slider(
            "Weight", 
            min_value=0.0, 
            max_value=10.0, 
            value=float(weight),
            step=0.1,
            key=f"weight_{method.get('id')}"
        )
        
        if new_weight != weight:
            update_method_weight(method.get('id'), new_weight, api_service)
        
        st.divider()

def methods_configuration_tab(api_service: APIService, app_state: AppState):
    """Method configuration tab"""
    
    st.subheader("Method Configuration")
    
    selected_method = st.session_state.get('selected_method')
    
    if not selected_method:
        st.info("Select a method from the Overview tab to configure its parameters.")
        return
    
    method_name = selected_method.get('name') or selected_method.get('method_name', 'Unknown Method')
    st.markdown(f"### Configuring: {method_name}")
    
    # Method activation toggle
    is_active = st.checkbox(
        "Method Active", 
        value=selected_method.get('is_active', True),
        key="method_active_toggle"
    )
    
    # Weight configuration
    weight = st.slider(
        "Method Weight",
        min_value=0.0,
        max_value=10.0,
        value=float(selected_method.get('weight', 1.0)),
        step=0.1,
        help="Higher weights give more importance to this method's signals"
    )
    
    # Sub-methods configuration
    st.subheader("Sub-Methods")
    
    # Load sub-methods if not already loaded
    method_id = selected_method.get('method_id') or selected_method.get('id')
    if f'sub_methods_{method_id}' not in st.session_state:
        load_sub_methods(method_id, api_service)
    
    sub_methods = st.session_state.get(f'sub_methods_{method_id}', [])
    
    if sub_methods:
        for sub_method in sub_methods:
            display_sub_method_config(sub_method)
    else:
        st.info("No sub-methods available for this method.")
    
    # Save configuration
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("💾 Save Configuration", use_container_width=True):
            save_method_configuration(selected_method, is_active, weight, api_service)
    
    with col2:
        if st.button("🔄 Reset to Defaults", use_container_width=True):
            reset_method_configuration(selected_method, api_service)

def methods_testing_tab(api_service: APIService, app_state: AppState):
    """Method testing tab"""
    
    st.subheader("Method Testing")
    st.info("Method testing functionality will be implemented here.")
    
    # Placeholder for testing functionality
    st.markdown("""
    **Planned Features:**
    - Individual method testing
    - Parameter optimization
    - Backtesting results
    - Performance analysis
    """)

def methods_comparison_tab(api_service: APIService, app_state: AppState):
    """Method comparison tab"""
    
    st.subheader("Method Comparison")
    st.info("Method comparison functionality will be implemented here.")
    
    # Placeholder for comparison functionality
    st.markdown("""
    **Planned Features:**
    - Side-by-side method comparison
    - Performance metrics comparison
    - Statistical analysis
    - Recommendation engine
    """)

def load_trading_methods(api_service: APIService, app_state: AppState):
    """Load trading methods from API"""
    
    try:
        instrument_id = app_state.selected_instrument.get('instrument_id')
        if not instrument_id:
            st.error("No instrument ID available")
            return
        
        with st.spinner("Loading trading methods..."):
            # This would be an actual API call
            # For now, using placeholder data
            methods = [
                {
                    'id': 1,
                    'method_id': 1,
                    'name': 'RSI Method',
                    'description': 'Relative Strength Index based trading method',
                    'is_active': True,
                    'weight': 2.5,
                    'success_rate': 65.2
                },
                {
                    'id': 2,
                    'method_id': 2,
                    'name': 'MACD Method',
                    'description': 'Moving Average Convergence Divergence method',
                    'is_active': False,
                    'weight': 1.8,
                    'success_rate': 58.7
                }
            ]
            
            st.session_state.trading_methods = methods
            st.success(f"Loaded {len(methods)} trading methods")
    
    except Exception as e:
        st.error(f"Failed to load trading methods: {str(e)}")

def filter_methods(methods: List[Dict], search_term: str, filter_active: str) -> List[Dict]:
    """Filter methods based on search term and active status"""
    
    filtered = methods
    
    # Apply search filter
    if search_term:
        filtered = [
            method for method in filtered
            if search_term.lower() in (method.get('name', '') + method.get('description', '')).lower()
        ]
    
    # Apply active status filter
    if filter_active != "All":
        is_active = filter_active == "Active"
        filtered = [
            method for method in filtered
            if method.get('is_active', True) == is_active
        ]
    
    return filtered

def load_sub_methods(method_id: int, api_service: APIService):
    """Load sub-methods for a specific method"""
    
    try:
        # Placeholder sub-methods data
        sub_methods = [
            {
                'id': 1,
                'name': 'RSI Oversold',
                'enabled': True,
                'parameters': {'period': 14, 'oversold_level': 30}
            },
            {
                'id': 2,
                'name': 'RSI Overbought',
                'enabled': True,
                'parameters': {'period': 14, 'overbought_level': 70}
            }
        ]
        
        st.session_state[f'sub_methods_{method_id}'] = sub_methods
    
    except Exception as e:
        st.error(f"Failed to load sub-methods: {str(e)}")

def display_sub_method_config(sub_method: Dict[str, Any]):
    """Display sub-method configuration"""
    
    with st.expander(f"📋 {sub_method.get('name', 'Unknown Sub-Method')}"):
        enabled = st.checkbox(
            "Enabled", 
            value=sub_method.get('enabled', True),
            key=f"sub_method_enabled_{sub_method.get('id')}"
        )
        
        parameters = sub_method.get('parameters', {})
        for param_name, param_value in parameters.items():
            if isinstance(param_value, (int, float)):
                st.number_input(
                    param_name.replace('_', ' ').title(),
                    value=param_value,
                    key=f"param_{sub_method.get('id')}_{param_name}"
                )

def update_method_weight(method_id: int, weight: float, api_service: APIService):
    """Update method weight"""
    
    try:
        # This would be an actual API call
        st.success(f"Updated method weight to {weight}")
    except Exception as e:
        st.error(f"Failed to update method weight: {str(e)}")

def save_method_configuration(method: Dict, is_active: bool, weight: float, api_service: APIService):
    """Save method configuration"""
    
    try:
        # This would be an actual API call
        st.success("Method configuration saved successfully!")
    except Exception as e:
        st.error(f"Failed to save configuration: {str(e)}")

def reset_method_configuration(method: Dict, api_service: APIService):
    """Reset method configuration to defaults"""
    
    try:
        # This would be an actual API call
        st.success("Method configuration reset to defaults!")
        st.rerun()
    except Exception as e:
        st.error(f"Failed to reset configuration: {str(e)}")
