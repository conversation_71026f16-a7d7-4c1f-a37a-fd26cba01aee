"""
Settings Page
Application settings, user preferences, and system configuration
"""

import streamlit as st
from typing import Dict, List, Any, Optional
from services.api_service import APIService
from store.app_state import AppState

def settings_page():
    """Main settings page"""
    
    st.title("⚙️ Settings")
    st.markdown("Configure application settings, user preferences, and system options.")
    
    # Get application state
    app_state: AppState = st.session_state.app_state
    api_service: APIService = st.session_state.api_service
    
    # Create tabs for different settings categories
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🎨 Appearance", 
        "📊 Trading", 
        "🔧 System", 
        "🔐 Security", 
        "📱 Notifications"
    ])
    
    with tab1:
        appearance_settings_tab(api_service, app_state)
    
    with tab2:
        trading_settings_tab(api_service, app_state)
    
    with tab3:
        system_settings_tab(api_service, app_state)
    
    with tab4:
        security_settings_tab(api_service, app_state)
    
    with tab5:
        notifications_settings_tab(api_service, app_state)

def appearance_settings_tab(api_service: APIService, app_state: AppState):
    """Appearance and theme settings"""
    
    st.subheader("Appearance Settings")
    
    # Theme settings
    st.markdown("### 🎨 Theme")
    
    col1, col2 = st.columns(2)
    
    with col1:
        theme = st.selectbox(
            "Application Theme",
            ["Light", "Dark", "Auto"],
            index=0,
            help="Choose the application theme"
        )
        
        color_scheme = st.selectbox(
            "Color Scheme",
            ["Default", "Blue", "Green", "Purple", "Orange"],
            index=0
        )
    
    with col2:
        font_size = st.selectbox(
            "Font Size",
            ["Small", "Medium", "Large"],
            index=1
        )
        
        compact_mode = st.checkbox(
            "Compact Mode",
            value=False,
            help="Use compact layout to show more information"
        )
    
    # Chart appearance (for non-chart pages)
    st.markdown("### 📊 Chart Appearance")
    
    col1, col2 = st.columns(2)
    
    with col1:
        chart_theme = st.selectbox(
            "Chart Theme",
            ["Light", "Dark"],
            index=0
        )
        
        grid_style = st.selectbox(
            "Grid Style",
            ["Solid", "Dashed", "Dotted", "None"],
            index=0
        )
    
    with col2:
        animation_enabled = st.checkbox(
            "Enable Animations",
            value=True,
            help="Enable chart animations and transitions"
        )
        
        high_contrast = st.checkbox(
            "High Contrast Mode",
            value=False,
            help="Use high contrast colors for better visibility"
        )
    
    # Save appearance settings
    if st.button("💾 Save Appearance Settings"):
        save_appearance_settings(theme, color_scheme, font_size, compact_mode, 
                                chart_theme, grid_style, animation_enabled, high_contrast, api_service)

def trading_settings_tab(api_service: APIService, app_state: AppState):
    """Trading-related settings"""
    
    st.subheader("Trading Settings")
    
    # Default trading parameters
    st.markdown("### 📈 Default Trading Parameters")
    
    col1, col2 = st.columns(2)
    
    with col1:
        default_risk_per_trade = st.slider(
            "Default Risk per Trade (%)",
            min_value=0.1,
            max_value=10.0,
            value=2.0,
            step=0.1
        )
        
        default_stop_loss = st.slider(
            "Default Stop Loss (%)",
            min_value=0.1,
            max_value=20.0,
            value=5.0,
            step=0.1
        )
    
    with col2:
        default_take_profit = st.slider(
            "Default Take Profit (%)",
            min_value=0.1,
            max_value=50.0,
            value=10.0,
            step=0.1
        )
        
        max_concurrent_positions = st.number_input(
            "Max Concurrent Positions",
            min_value=1,
            max_value=20,
            value=5
        )
    
    # Commission and fees
    st.markdown("### 💰 Commission and Fees")
    
    col1, col2 = st.columns(2)
    
    with col1:
        default_commission = st.number_input(
            "Default Commission per Trade ($)",
            min_value=0.0,
            value=5.0,
            step=0.1
        )
    
    with col2:
        default_slippage = st.number_input(
            "Default Slippage (%)",
            min_value=0.0,
            value=0.1,
            step=0.01
        )
    
    # Method preferences
    st.markdown("### 🎯 Method Preferences")
    
    auto_enable_methods = st.checkbox(
        "Auto-enable new methods",
        value=False,
        help="Automatically enable new trading methods when they are added"
    )
    
    method_weight_normalization = st.checkbox(
        "Normalize method weights",
        value=True,
        help="Automatically normalize method weights to sum to 1.0"
    )
    
    # Save trading settings
    if st.button("💾 Save Trading Settings"):
        save_trading_settings(default_risk_per_trade, default_stop_loss, default_take_profit,
                            max_concurrent_positions, default_commission, default_slippage,
                            auto_enable_methods, method_weight_normalization, api_service)

def system_settings_tab(api_service: APIService, app_state: AppState):
    """System and performance settings"""
    
    st.subheader("System Settings")
    
    # API settings
    st.markdown("### 🔗 API Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        api_timeout = st.number_input(
            "API Timeout (seconds)",
            min_value=5,
            max_value=300,
            value=30
        )
        
        max_retries = st.number_input(
            "Max API Retries",
            min_value=0,
            max_value=10,
            value=3
        )
    
    with col2:
        cache_duration = st.number_input(
            "Cache Duration (minutes)",
            min_value=1,
            max_value=60,
            value=5
        )
        
        auto_refresh_interval = st.number_input(
            "Auto Refresh Interval (seconds)",
            min_value=5,
            max_value=300,
            value=30
        )
    
    # Database settings
    st.markdown("### 🗄️ Database Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        backup_frequency = st.selectbox(
            "Backup Frequency",
            ["Never", "Daily", "Weekly", "Monthly"],
            index=1
        )
        
        max_backup_files = st.number_input(
            "Max Backup Files",
            min_value=1,
            max_value=50,
            value=10
        )
    
    with col2:
        vacuum_frequency = st.selectbox(
            "Database Vacuum Frequency",
            ["Never", "Weekly", "Monthly"],
            index=1
        )
        
        enable_wal_mode = st.checkbox(
            "Enable WAL Mode",
            value=True,
            help="Enable Write-Ahead Logging for better performance"
        )
    
    # Performance settings
    st.markdown("### ⚡ Performance Settings")
    
    col1, col2 = st.columns(2)
    
    with col1:
        max_data_points = st.number_input(
            "Max Data Points to Load",
            min_value=100,
            max_value=10000,
            value=1000
        )
    
    with col2:
        enable_parallel_processing = st.checkbox(
            "Enable Parallel Processing",
            value=True,
            help="Use multiple CPU cores for calculations"
        )
    
    # Save system settings
    if st.button("💾 Save System Settings"):
        save_system_settings(api_timeout, max_retries, cache_duration, auto_refresh_interval,
                           backup_frequency, max_backup_files, vacuum_frequency, enable_wal_mode,
                           max_data_points, enable_parallel_processing, api_service)

def security_settings_tab(api_service: APIService, app_state: AppState):
    """Security and authentication settings"""
    
    st.subheader("Security Settings")
    
    # Authentication
    st.markdown("### 🔐 Authentication")
    
    enable_authentication = st.checkbox(
        "Enable Authentication",
        value=False,
        help="Require login to access the application"
    )
    
    if enable_authentication:
        col1, col2 = st.columns(2)
        
        with col1:
            session_timeout = st.number_input(
                "Session Timeout (minutes)",
                min_value=5,
                max_value=480,
                value=60
            )
        
        with col2:
            max_login_attempts = st.number_input(
                "Max Login Attempts",
                min_value=1,
                max_value=10,
                value=3
            )
    
    # API Security
    st.markdown("### 🔑 API Security")
    
    enable_api_key = st.checkbox(
        "Require API Key",
        value=False,
        help="Require API key for backend access"
    )
    
    if enable_api_key:
        api_key = st.text_input(
            "API Key",
            type="password",
            help="Enter your API key"
        )
    
    # Data encryption
    st.markdown("### 🔒 Data Encryption")
    
    encrypt_sensitive_data = st.checkbox(
        "Encrypt Sensitive Data",
        value=True,
        help="Encrypt sensitive data in the database"
    )
    
    # Save security settings
    if st.button("💾 Save Security Settings"):
        save_security_settings(enable_authentication, session_timeout if enable_authentication else None,
                             max_login_attempts if enable_authentication else None,
                             enable_api_key, api_key if enable_api_key else None,
                             encrypt_sensitive_data, api_service)

def notifications_settings_tab(api_service: APIService, app_state: AppState):
    """Notification settings"""
    
    st.subheader("Notification Settings")
    
    # Email notifications
    st.markdown("### 📧 Email Notifications")
    
    enable_email_notifications = st.checkbox(
        "Enable Email Notifications",
        value=False
    )
    
    if enable_email_notifications:
        email_address = st.text_input("Email Address")
        
        st.markdown("**Notification Types:**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            notify_simulation_complete = st.checkbox("Simulation Complete", value=True)
            notify_method_alerts = st.checkbox("Method Alerts", value=True)
        
        with col2:
            notify_system_errors = st.checkbox("System Errors", value=True)
            notify_data_updates = st.checkbox("Data Updates", value=False)
    
    # Browser notifications
    st.markdown("### 🔔 Browser Notifications")
    
    enable_browser_notifications = st.checkbox(
        "Enable Browser Notifications",
        value=True
    )
    
    if enable_browser_notifications:
        st.markdown("**Browser Notification Types:**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            browser_notify_trades = st.checkbox("Trade Signals", value=True)
            browser_notify_alerts = st.checkbox("System Alerts", value=True)
        
        with col2:
            browser_notify_updates = st.checkbox("Data Updates", value=False)
            browser_notify_errors = st.checkbox("Errors", value=True)
    
    # Save notification settings
    if st.button("💾 Save Notification Settings"):
        save_notification_settings(enable_email_notifications, 
                                  email_address if enable_email_notifications else None,
                                  notify_simulation_complete if enable_email_notifications else False,
                                  notify_method_alerts if enable_email_notifications else False,
                                  notify_system_errors if enable_email_notifications else False,
                                  notify_data_updates if enable_email_notifications else False,
                                  enable_browser_notifications,
                                  browser_notify_trades if enable_browser_notifications else False,
                                  browser_notify_alerts if enable_browser_notifications else False,
                                  browser_notify_updates if enable_browser_notifications else False,
                                  browser_notify_errors if enable_browser_notifications else False,
                                  api_service)

# Helper Functions

def save_appearance_settings(theme, color_scheme, font_size, compact_mode, 
                           chart_theme, grid_style, animation_enabled, high_contrast, api_service):
    """Save appearance settings"""
    try:
        # This would be an actual API call
        st.success("✅ Appearance settings saved successfully!")
    except Exception as e:
        st.error(f"Failed to save appearance settings: {str(e)}")

def save_trading_settings(risk_per_trade, stop_loss, take_profit, max_positions,
                        commission, slippage, auto_enable, normalize_weights, api_service):
    """Save trading settings"""
    try:
        # This would be an actual API call
        st.success("✅ Trading settings saved successfully!")
    except Exception as e:
        st.error(f"Failed to save trading settings: {str(e)}")

def save_system_settings(api_timeout, max_retries, cache_duration, auto_refresh,
                       backup_freq, max_backups, vacuum_freq, wal_mode,
                       max_data_points, parallel_processing, api_service):
    """Save system settings"""
    try:
        # This would be an actual API call
        st.success("✅ System settings saved successfully!")
    except Exception as e:
        st.error(f"Failed to save system settings: {str(e)}")

def save_security_settings(enable_auth, session_timeout, max_attempts,
                         enable_api_key, api_key, encrypt_data, api_service):
    """Save security settings"""
    try:
        # This would be an actual API call
        st.success("✅ Security settings saved successfully!")
    except Exception as e:
        st.error(f"Failed to save security settings: {str(e)}")

def save_notification_settings(enable_email, email_address, notify_sim, notify_method,
                             notify_error, notify_data, enable_browser, browser_trades,
                             browser_alerts, browser_updates, browser_errors, api_service):
    """Save notification settings"""
    try:
        # This would be an actual API call
        st.success("✅ Notification settings saved successfully!")
    except Exception as e:
        st.error(f"Failed to save notification settings: {str(e)}")
