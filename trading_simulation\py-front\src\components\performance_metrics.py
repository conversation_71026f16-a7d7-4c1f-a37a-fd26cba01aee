"""
Performance Metrics Component
Streamlit component for displaying performance metrics and statistics
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
from utils.formatters import format_percentage, format_currency, format_number

def render_performance_metrics(
    metrics: Dict[str, Any],
    title: str = "Performance Metrics",
    show_charts: bool = True,
    container_key: str = "performance_metrics"
) -> None:
    """
    Render performance metrics component
    
    Args:
        metrics: Dictionary of performance metrics
        title: Component title
        show_charts: Whether to show performance charts
        container_key: Unique key for the container
    """
    
    st.markdown(f"### 📊 {title}")
    
    # Key metrics in columns
    render_key_metrics(metrics)
    
    if show_charts:
        # Performance charts
        render_performance_charts(metrics, container_key)
    
    # Detailed metrics table
    render_detailed_metrics(metrics)

def render_key_metrics(metrics: Dict[str, Any]) -> None:
    """Render key performance metrics in columns"""
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_return = metrics.get('total_return', 0)
        return_delta = metrics.get('return_delta', 0)
        st.metric(
            "Total Return",
            format_percentage(total_return),
            delta=format_percentage(return_delta) if return_delta != 0 else None
        )
    
    with col2:
        win_rate = metrics.get('win_rate', 0)
        win_rate_delta = metrics.get('win_rate_delta', 0)
        st.metric(
            "Win Rate",
            format_percentage(win_rate),
            delta=format_percentage(win_rate_delta) if win_rate_delta != 0 else None
        )
    
    with col3:
        profit_factor = metrics.get('profit_factor', 0)
        profit_factor_delta = metrics.get('profit_factor_delta', 0)
        st.metric(
            "Profit Factor",
            format_number(profit_factor),
            delta=format_number(profit_factor_delta) if profit_factor_delta != 0 else None
        )
    
    with col4:
        total_trades = metrics.get('total_trades', 0)
        trades_delta = metrics.get('trades_delta', 0)
        st.metric(
            "Total Trades",
            str(total_trades),
            delta=str(trades_delta) if trades_delta != 0 else None
        )

def render_performance_charts(metrics: Dict[str, Any], container_key: str) -> None:
    """Render performance charts"""
    
    # Create tabs for different chart types
    tab1, tab2, tab3 = st.tabs(["📈 Equity Curve", "📊 Returns Distribution", "🎯 Win/Loss Ratio"])
    
    with tab1:
        render_equity_curve(metrics, container_key)
    
    with tab2:
        render_returns_distribution(metrics, container_key)
    
    with tab3:
        render_win_loss_chart(metrics, container_key)

def render_equity_curve(metrics: Dict[str, Any], container_key: str) -> None:
    """Render equity curve chart"""
    
    equity_data = metrics.get('equity_curve', [])
    
    if not equity_data:
        st.info("No equity curve data available")
        return
    
    # Convert to DataFrame
    df = pd.DataFrame(equity_data)
    
    if 'date' not in df.columns or 'equity' not in df.columns:
        st.error("Invalid equity curve data format")
        return
    
    # Create line chart
    fig = px.line(
        df,
        x='date',
        y='equity',
        title='Portfolio Equity Over Time',
        labels={'equity': 'Portfolio Value ($)', 'date': 'Date'}
    )
    
    fig.update_layout(
        xaxis_title="Date",
        yaxis_title="Portfolio Value ($)",
        hovermode='x unified'
    )
    
    st.plotly_chart(fig, use_container_width=True, key=f"{container_key}_equity")

def render_returns_distribution(metrics: Dict[str, Any], container_key: str) -> None:
    """Render returns distribution chart"""
    
    returns_data = metrics.get('returns_distribution', [])
    
    if not returns_data:
        st.info("No returns distribution data available")
        return
    
    # Create histogram
    fig = px.histogram(
        x=returns_data,
        nbins=30,
        title='Returns Distribution',
        labels={'x': 'Return (%)', 'count': 'Frequency'}
    )
    
    fig.update_layout(
        xaxis_title="Return (%)",
        yaxis_title="Frequency",
        showlegend=False
    )
    
    st.plotly_chart(fig, use_container_width=True, key=f"{container_key}_returns")

def render_win_loss_chart(metrics: Dict[str, Any], container_key: str) -> None:
    """Render win/loss ratio chart"""
    
    winning_trades = metrics.get('winning_trades', 0)
    losing_trades = metrics.get('losing_trades', 0)
    
    if winning_trades == 0 and losing_trades == 0:
        st.info("No trade data available")
        return
    
    # Create pie chart
    labels = ['Winning Trades', 'Losing Trades']
    values = [winning_trades, losing_trades]
    colors = ['#2ECC71', '#E74C3C']
    
    fig = go.Figure(data=[go.Pie(
        labels=labels,
        values=values,
        marker_colors=colors,
        hole=0.3
    )])
    
    fig.update_layout(
        title='Win/Loss Ratio',
        annotations=[dict(text=f'{winning_trades + losing_trades}<br>Total Trades', 
                         x=0.5, y=0.5, font_size=16, showarrow=False)]
    )
    
    st.plotly_chart(fig, use_container_width=True, key=f"{container_key}_winloss")

def render_detailed_metrics(metrics: Dict[str, Any]) -> None:
    """Render detailed metrics table"""
    
    with st.expander("📋 Detailed Metrics"):
        
        # Organize metrics into categories
        basic_metrics = {
            'Total Return (%)': format_percentage(metrics.get('total_return', 0)),
            'Win Rate (%)': format_percentage(metrics.get('win_rate', 0)),
            'Profit Factor': format_number(metrics.get('profit_factor', 0)),
            'Total Trades': str(metrics.get('total_trades', 0)),
            'Winning Trades': str(metrics.get('winning_trades', 0)),
            'Losing Trades': str(metrics.get('losing_trades', 0)),
        }
        
        risk_metrics = {
            'Sharpe Ratio': format_number(metrics.get('sharpe_ratio', 0)),
            'Max Drawdown (%)': format_percentage(metrics.get('max_drawdown', 0)),
            'Volatility (%)': format_percentage(metrics.get('volatility', 0)),
            'Beta': format_number(metrics.get('beta', 0)),
            'Alpha (%)': format_percentage(metrics.get('alpha', 0)),
            'Information Ratio': format_number(metrics.get('information_ratio', 0)),
        }
        
        trade_metrics = {
            'Average Win (%)': format_percentage(metrics.get('avg_win', 0)),
            'Average Loss (%)': format_percentage(metrics.get('avg_loss', 0)),
            'Largest Win (%)': format_percentage(metrics.get('largest_win', 0)),
            'Largest Loss (%)': format_percentage(metrics.get('largest_loss', 0)),
            'Average Trade Duration': str(metrics.get('avg_trade_duration', 'N/A')),
            'Recovery Factor': format_number(metrics.get('recovery_factor', 0)),
        }
        
        # Display metrics in tabs
        tab1, tab2, tab3 = st.tabs(["📊 Basic", "⚠️ Risk", "📈 Trade"])
        
        with tab1:
            render_metrics_table(basic_metrics)
        
        with tab2:
            render_metrics_table(risk_metrics)
        
        with tab3:
            render_metrics_table(trade_metrics)

def render_metrics_table(metrics: Dict[str, str]) -> None:
    """Render metrics as a table"""
    
    # Convert to DataFrame for better display
    df = pd.DataFrame(list(metrics.items()), columns=['Metric', 'Value'])
    
    # Display as table without index
    st.dataframe(df, use_container_width=True, hide_index=True)

def render_performance_summary(
    metrics: Dict[str, Any],
    size: str = "normal"
) -> None:
    """
    Render compact performance summary
    
    Args:
        metrics: Performance metrics
        size: Size of the summary ('compact', 'normal', 'large')
    """
    
    if size == "compact":
        # Single row of key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Return", format_percentage(metrics.get('total_return', 0)))
        with col2:
            st.metric("Win Rate", format_percentage(metrics.get('win_rate', 0)))
        with col3:
            st.metric("Trades", str(metrics.get('total_trades', 0)))
        with col4:
            st.metric("Profit Factor", format_number(metrics.get('profit_factor', 0)))
    
    elif size == "large":
        # Extended metrics display
        render_key_metrics(metrics)
        
        # Additional row
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Sharpe Ratio", format_number(metrics.get('sharpe_ratio', 0)))
        with col2:
            st.metric("Max Drawdown", format_percentage(metrics.get('max_drawdown', 0)))
        with col3:
            st.metric("Volatility", format_percentage(metrics.get('volatility', 0)))
        with col4:
            st.metric("Recovery Factor", format_number(metrics.get('recovery_factor', 0)))
    
    else:
        # Normal size - just key metrics
        render_key_metrics(metrics)

def calculate_performance_score(metrics: Dict[str, Any]) -> float:
    """
    Calculate overall performance score
    
    Args:
        metrics: Performance metrics
        
    Returns:
        Performance score (0-100)
    """
    
    # Weighted scoring based on key metrics
    total_return = metrics.get('total_return', 0)
    win_rate = metrics.get('win_rate', 0)
    profit_factor = metrics.get('profit_factor', 0)
    sharpe_ratio = metrics.get('sharpe_ratio', 0)
    max_drawdown = metrics.get('max_drawdown', 0)
    
    # Normalize and weight metrics
    return_score = min(max(total_return / 50 * 100, 0), 100) * 0.3
    win_rate_score = win_rate * 0.2
    profit_factor_score = min(max((profit_factor - 1) / 2 * 100, 0), 100) * 0.2
    sharpe_score = min(max(sharpe_ratio / 3 * 100, 0), 100) * 0.2
    drawdown_score = max(100 - abs(max_drawdown), 0) * 0.1
    
    total_score = return_score + win_rate_score + profit_factor_score + sharpe_score + drawdown_score
    
    return min(max(total_score, 0), 100)

def render_performance_score(metrics: Dict[str, Any]) -> None:
    """Render performance score gauge"""
    
    score = calculate_performance_score(metrics)
    
    # Determine color based on score
    if score >= 80:
        color = "#2ECC71"  # Green
        grade = "Excellent"
    elif score >= 60:
        color = "#F39C12"  # Orange
        grade = "Good"
    elif score >= 40:
        color = "#E67E22"  # Dark Orange
        grade = "Fair"
    else:
        color = "#E74C3C"  # Red
        grade = "Poor"
    
    # Create gauge chart
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = score,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': f"Performance Score<br><span style='font-size:0.8em;color:gray'>{grade}</span>"},
        delta = {'reference': 50},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': color},
            'steps': [
                {'range': [0, 40], 'color': "lightgray"},
                {'range': [40, 60], 'color': "gray"},
                {'range': [60, 80], 'color': "lightblue"},
                {'range': [80, 100], 'color': "lightgreen"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))
    
    fig.update_layout(height=300)
    
    st.plotly_chart(fig, use_container_width=True)
